//
// Created by Fsyrl on 24-9-14.
//

#pragma once

#include "PsiStruct.h"
#include "PsiTcpShmServerCommon.h"
#include "PsiTraderMsgType.h"
#include <csignal>
using namespace std;
using namespace tcpshm;

class Server;
using TSServer = TcpShmServer<Server>;

class Server : public TSServer {
public:
    static volatile bool stopped;

    Server(const std::string &ptcp_dir, const ServerConf &conf)
        : TSServer(ptcp_dir, conf.ServerName, conf), conf_(conf){
        // capture SIGTERM to gracefully stop the server
        // we can also send other signals to crash the server and see how it recovers on restart
        m_index = conf.Index;
        m_cpuCore = conf.CpuCore;
        m_serverIp = conf.ServerIp;
        signal(SIGTERM, Server::SignalHandler);
    }

    static void SignalHandler(int s) {
        stopped = true;
    }

    void Run() {
        if (!Start(conf_.ServerIp.c_str(), conf_.ListenPort)) return;
        vector<thread> threads;
        // create threads for polling tcp
        threads.reserve(conf_.MaxTcpGrps + conf_.MaxShmGrps);
        for (int i = 0; i < conf_.MaxTcpGrps; i++) {
            threads.emplace_back([this, i]() {
                while (!stopped) {
                    PollTcp(now(), i);
                }
            });
        }

        // create threads for polling shm
        for (int i = 0; i < conf_.MaxShmGrps; i++) {
            threads.emplace_back([this, i]() {
                while (!stopped) {
                    PollShm(i);
                }
            });
        }

        while (!stopped) {
            PollCtl(now());
        }
        for (auto &thr: threads) {
            thr.join();
        }
        Stop();
        cout << "TcpShm Server stopped" << endl;
    }
    template<typename T>
    bool RspByReqId(int req_id, T* msg) {
        auto it = reqMap_.find(req_id);
        if (it == reqMap_.end()) {
            return false;
        }
        auto [conn,name]  = it->second;
        reqMap_.erase(it);
        if(conn->IsClosed() || strcmp(name,conn->GetRemoteName())!=0) {
            return false;
        }
        MsgHeader* header = conn->Alloc(sizeof(T));
        if(!header) {
            return false;
        }
        header->msg_type = T::msg_type;
        memcpy(header->GetMsgBodyPtr<T>(), msg, sizeof(T));
        // conn->Pop(); ����Ҫpop��Ӧ����Ϊ�ͻ�������ͬ��ʱPop���첽ʹ��goto ACK
        conn->Push();
        return true;
    }

private:
    friend TSServer;

    // called with Start()
    // reporting errors on Starting the server
    void OnSystemError(const char *errno_msg, int sys_errno) {
        cout << "System Error: " << errno_msg << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called by CTL thread
    // if accept the connection, set user_data in login_rsp and return grpid(start from 0) with respect to tcp or shm
    // else set error_msg in login_rsp if possible, and return -1
    // Note that even if we accept it here, there could be other errors on handling the login,
    // so we have to wait OnClientLogon for confirmation
    int OnNewConnection(const struct sockaddr_in &addr, const LoginMsg *login, LoginRspMsg *login_rsp) {
        cout << "New Connection from: " << inet_ntoa(addr.sin_addr) << ":" << ntohs(addr.sin_port)
                << ", name: " << login->client_name << ", use_shm: " << (bool) login->use_shm << endl;
        // here we simply hash client name to uniformly map to each group
        auto hh = hash<string>{}(string(login->client_name));
        if (login->use_shm) {
            if (conf_.MaxShmGrps > 0) {
                return hh % conf_.MaxShmGrps;
            } else {
                strcpy(login_rsp->error_msg, "Shm disabled");
                return -1;
            }
        } else {
            if (conf_.MaxTcpGrps > 0) {
                return hh % conf_.MaxTcpGrps;
            } else {
                strcpy(login_rsp->error_msg, "Tcp disabled");
                return -1;
            }
        }
    }

    // called by CTL thread
    // ptcp or shm files can't be open or are corrupt
    void OnClientFileError(Connection &conn, const char *reason, int sys_errno) {
        cout << "Client file errno, name: " << conn.GetRemoteName() << " reason: " << reason
                << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called by CTL thread
    // server and client ptcp sequence number don't match, we need to fix it manually
    void OnSeqNumberMismatch(Connection &conn,
                             uint32_t local_ack_seq,
                             uint32_t local_seq_start,
                             uint32_t local_seq_end,
                             uint32_t remote_ack_seq,
                             uint32_t remote_seq_start,
                             uint32_t remote_seq_end) {
        cout << "Client seq number mismatch, name: " << conn.GetRemoteName() << " ptcp file: " << conn.GetPtcpFile()
                << " local_ack_seq: " << local_ack_seq << " local_seq_start: " << local_seq_start
                << " local_seq_end: " << local_seq_end << " remote_ack_seq: " << remote_ack_seq
                << " remote_seq_start: " << remote_seq_start << " remote_seq_end: " << remote_seq_end << endl;
    }

    // called by CTL thread
    // confirmation for client logon
    void OnClientLogon(const struct sockaddr_in &addr, Connection &conn) {
        cout << "Client Logon from: " << inet_ntoa(addr.sin_addr) << ":" << ntohs(addr.sin_port)
                << ", name: " << conn.GetRemoteName() << endl;
    }

    // called by CTL thread
    // client is disconnected
    void OnClientDisconnected(Connection &conn, const char *reason, int sys_errno) {
        cout << "Client disconnected,.name: " << conn.GetRemoteName() << " reason: " << reason
                << " syserrno: " << strerror(sys_errno) << endl;
    }

    // called by APP thread
    void OnClientMsg(Connection &conn, MsgHeader *recv_header) {
        MsgHeader *send_header = nullptr;
        switch (recv_header->msg_type) {
        case MSG_PING_TYPE: {
            auto* ping_req = recv_header->GetMsgBodyPtr<PingReq>();
            cout << "Got ping request from user:" << ping_req->token <<
                    ", seq_no: " << ping_req->seq_no <<
                    ", send_time: " << ping_req->send_time <<
                    ", ack_time: " << tcpshm::now() <<
                    " latency: " << tcpshm::now() - ping_req->send_time << " ms" << endl;

            conn.TrySendMsg<PingResp>([ping_req](PingResp *resp) {
                resp->seq_no = ping_req->seq_no;  // 回传相同的序列号
                resp->ack_time = tcpshm::now();   // 设置确认时间
                strcpy(resp->val, "pong");
            }, false);
            break;
        }
        // case MSG_TRADER_TYPE: {
        //     auto *req = recv_header->GetMsgBodyPtr<TraderReq>();
        //     reqMap_[req->_doTrader.request_id]=make_pair(&conn,conn.GetRemoteName());
        //     goto ACK;
        // }
        //     if (false) {
        //     ACK:
        //         // conn.TrySendMsg<ReceiveResp>([](ReceiveResp *resp) {
        //         // }, false);
        //         // break;
        //     }
        default: {
            cout << "Unknown msg type: " << recv_header->msg_type << endl;
            break;
        }
        }
    }

private:
    const ServerConf &conf_;

    typedef  int nReqId ;
    typedef  pair<Connection*,const char*> ReqMapValue;
    psi_hashmap<nReqId,ReqMapValue> reqMap_;//������ָ��Ͷ�Ӧ�Ŀͻ�������ָ�룬���ܱ��ͻ��������ж϶������ͻ���ռ����������ӣ�������Ҫ�ڷ���֮ǰ����Ƿ���֮ǰ�Ŀͻ��ˡ�
    psi_hashmap<string,nReqId> clientMap;
    //PsiRunner *runner_;
public:
    int m_index = 0; // index
    int m_cpuCore = 0; // �󶨺���
    std::string m_serverIp; // ������ip
};
