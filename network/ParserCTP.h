﻿/*!
 * \file ParserCTP.h
 * \project	WonderTrader
 *
 * \author Wesley
 * \date 2020/03/30
 * 
 * \brief 
 */
#pragma once
#include "ThostFtdcMdApi.h"
#include <map>


#include "PsiDataDef.hpp"
#include "PsiContractInfo.hpp"
#include "PsiVariant.hpp"
#include "StrUtil.hpp"
#include "TimeUtils.hpp"
#include "PsiBaseDataMgr.h"
#include "PsiDataMgr.h"
#include "PsiResourceMgr.h"
#include "PsiParserDataMgr.h"

#include <boost/filesystem.hpp>
#include <boost/asio.hpp>
#include <boost/asio/executor_work_guard.hpp>
#include "PsiMarcos.h"

#include <boost/filesystem.hpp>

class ParserCTP : public CThostFtdcMdSpi
{
public:
	ParserCTP();
	~ParserCTP();

public:
	enum LoginStatus
	{
		LS_NOTLOGIN,
		LS_LOGINING,
		LS_LOGINED
	};

//IQuoteParser 接口
public:
	bool init();

	void release();

	bool connect();

	bool disconnect();

	bool isConnected();

	void subscribe(const CodeSet &vecSymbols);

	void unsubscribe(const CodeSet &vecSymbols);

	void registerSpi(PsiBaseDataMgr *baseDataMgr, PsiDataMgr *dataMgr, PsiParserDataMgr *parserDataMgr, const char* parserId, int computeCoreSize);


//CThostFtdcMdSpi 接口
public:
	virtual void OnRspError( CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast );

	virtual void OnFrontConnected();

	virtual void OnRspUserLogin( CThostFtdcRspUserLoginField *pRspUserLogin, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast );

	///登出请求响应
	virtual void OnRspUserLogout(CThostFtdcUserLogoutField *pUserLogout, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast);

	virtual void OnFrontDisconnected( int nReason );

	virtual void OnRspUnSubMarketData( CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast );

	virtual void OnRtnDepthMarketData( CThostFtdcDepthMarketDataField *pDepthMarketData );

	virtual void OnRspSubMarketData( CThostFtdcSpecificInstrumentField *pSpecificInstrument, CThostFtdcRspInfoField *pRspInfo, int nRequestID, bool bIsLast );

	virtual void OnHeartBeatWarning( int nTimeLapse );

private:
	/*
	 *	发送登录请求
	 */
	void ReqUserLogin();
	/*
	 *	订阅品种行情
	 */
	void DoSubscribeMD();
	/*
	 *	检查错误信息
	 */
	bool IsErrorRspInfo(CThostFtdcRspInfoField *pRspInfo);


private:
	uint32_t			m_uTradingDate;
	CThostFtdcMdApi*	m_pUserAPI;

	std::string			m_strFrontAddr;
	std::string			m_strBroker;
	std::string			m_strUserID;
	std::string			m_strPassword;
	std::string			m_strFlowDir;
	bool 				m_bLocaltime;	//是否使用本地时间戳

	CodeSet				m_filterSubs;

	int					m_iRequestID;

    std::string         m_parserId; // 解析器ID
    bool                m_isConnected = false;
    LoginStatus			m_loginState;

    PsiBaseDataMgr*      mp_baseDataMgr; // 基础数据管理器
    PsiDataMgr*          mp_dataMgr; // 数据管理器
    PsiParserDataMgr*    mp_parserDataMgr; // 行情数据管理
    int                  m_computeCoreSize = 0; // 计算核心数
};

