 #pragma once
#include <windows.h>  // 替代 unistd.h 和内存操作
#include <io.h>
#include <memoryapi.h>  // 替代 sys/mman.h
#include <sys/stat.h>
#include <fcntl.h>
#define PROT_READ  0x1
#define PROT_WRITE 0x2
#define MAP_SHARED 0x01
#define MAP_FAILED ((void*)-1)
template<class T>
T* MyMmap(const char* filename, bool use_shm, const char** error_msg,size_t size=sizeof(T)) {
    HANDLE hFile = CreateFileA(filename,
                               GENERIC_READ | GENERIC_WRITE,
                               FILE_SHARE_READ | FILE_SHARE_WRITE,
                               NULL,
                               OPEN_ALWAYS,
                               FILE_ATTRIBUTE_NORMAL,
                               NULL);
    if(hFile == INVALID_HANDLE_VALUE) {
        *error_msg = "CreateFile";
        return nullptr;
    }
    HANDLE hMap = CreateFileMapping(hFile, NULL, PAGE_READWRITE, 0, size, NULL);
    if(!hMap) {
        *error_msg = "CreateFileMapping";
        CloseHandle(hFile);
        return nullptr;
    }
    T* ret = (T*)MapViewOfFile(hMap, FILE_MAP_ALL_ACCESS, 0, 0, size);
    CloseHandle(hMap);
    CloseHandle(hFile);
    if(!ret) {
        *error_msg = "MapViewOfFile";
        return nullptr;
    }
    return ret;
}

template<class T>
void MyUnmap(void* addr,size_t size=sizeof(T)) {
    UnmapViewOfFile(addr);
}
