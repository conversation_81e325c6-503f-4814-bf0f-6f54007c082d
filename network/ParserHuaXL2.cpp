/*!
 * \file ParserHuaX.cpp
 * \project	WonderTrader
 *
 * \author Wesley
 * \date 2020/03/30
 *
 * \brief
 */
#include "ParserHuaXL2.h"
#include "lockfree.hpp"
#include <filesystem>

extern "C"
{
	EXPORT_FLAG ParserHuaXL2* createParser()
	{
        ParserHuaXL2* parser = new ParserHuaXL2();
		return parser;
	}

	EXPORT_FLAG void deleteParser(ParserHuaXL2*& parser)
	{
		if (NULL != parser)
		{
			delete parser;
			parser = NULL;
		}
	}
};


ParserHuaXL2::ParserHuaXL2()
	:mp_api(NULL)
    , m_cachedMode(false)
    , m_proxy(false)
    , m_rxqCapacity(0)
    , m_befvi(false)
	, m_iRequestID(0)
	, _uTradingDate(0)
    , mp_baseDataMgr(NULL)
    , mp_dataMgr(NULL)
    , mp_parserDataMgr(NULL)
    , m_computeThreadCore(0)
    , m_orderWorker(NULL)
    , m_transWorker(NULL)
    , m_work(m_asyncio.get_executor())
{

}


ParserHuaXL2::~ParserHuaXL2()
{
    mp_api = NULL;
}

bool ParserHuaXL2::init()
{
    if(!mp_baseDataMgr){
        // loge("ParserHuaXL2::init mp_baseDataMgr is NULL");
        return false;
    }
    PSIParserParamsStruct paramsStruct = mp_baseDataMgr->getParserParams(m_parserId.c_str());
    if(paramsStruct.parser_id.empty()){
        // loge("ParserHuaXL2::init paramsStruct.parser id is empty");
        return false;
    }
    m_front = paramsStruct.front;
    m_interfaceIp = paramsStruct.interface_ip;
    m_market = paramsStruct.market;
    m_strUser = paramsStruct.str_user;
    m_strPass = paramsStruct.str_pass;
    m_strFlowDir = paramsStruct.str_flow_dir;
    m_subscribeType = paramsStruct.subscribe_type;
    m_subMode = paramsStruct.sub_mode;
    m_cachedMode = paramsStruct.cached_mode;
    m_proxy = paramsStruct.proxy;
    m_cpuCores = paramsStruct.cpu_cores;
    m_interfaceName = paramsStruct.interface_name;
    m_sourceIp = paramsStruct.source_ip;
    m_rxqCapacity = paramsStruct.rxq_capacity;
    m_befvi = paramsStruct.befvi;
    m_gpsize = paramsStruct.gpsize;
    m_codes = paramsStruct.codes;
    m_orderCpuCore = paramsStruct.order_cpu_core;
    m_transCpuCore = paramsStruct.trans_cpu_core;
    if (m_gpsize == 0)
        m_gpsize = 1000000;
	if (m_strFlowDir.empty())
        m_strFlowDir = "HuaL2Flow";

	if (m_subMode.empty())
        m_subMode = "0";
    // logi("ParserHuaXL2::init m_subMode[{}]",m_subMode.c_str());
    m_strFlowDir = StrUtil::standardisePath(m_strFlowDir);

	std::string path = StrUtil::printf("%s/%s/", m_strFlowDir.c_str(), m_strUser.c_str());
	boost::filesystem::create_directories(path.c_str());

    // connect();
	return true;
}

void ParserHuaXL2::release()
{
	disconnect();
}

bool ParserHuaXL2::connect()
{

    // ��Ҫ�жϵ�ǰ��������ģʽ
    if(m_isConnected) return false;
    m_isConnected = true;
    if (m_front.find("udp://") != std::string::npos) {
        mp_api = CTORATstpLev2MdApi::CreateTstpLev2MdApi(TORA_TSTP_MST_MCAST, m_cachedMode);
        // befviģʽ
        if (m_befvi) mp_api->RegisterMulticast((char*)m_front.c_str(), (char*)m_interfaceIp.c_str(),
                                            NULL, m_interfaceName.c_str(), m_rxqCapacity, true);
        else mp_api->RegisterMulticast((char*)m_front.c_str(), (char*)m_interfaceIp.c_str(),"");
    } else {
        if (m_proxy) mp_api = CTORATstpLev2MdApi::CreateTstpLev2MdApi(TORA_TSTP_MST_PROXY, m_cachedMode);
        else mp_api = CTORATstpLev2MdApi::CreateTstpLev2MdApi(TORA_TSTP_MST_TCP,m_cachedMode);
        mp_api->RegisterFront((char*)m_front.c_str());
    }
    mp_api->RegisterSpi(this);
    mp_api->Init(m_cpuCores.c_str());
    // mp_api->Join();
	return true;
}

/**
 * ����
 * @return
 */
bool ParserHuaXL2::run(){
    PsiArray* ay = mp_baseDataMgr->getContracts();
    CodeSet contractSet;
    for(auto it = ay->begin(); it != ay->end(); it++) {
        PsiContractInfo *cInfo = STATIC_CONVERT(*it, PsiContractInfo *);
        if(NULL != cInfo){
            contractSet.insert(cInfo->getFullCode());
        }
    }
    ay->release();
    // logi("[ParserHuaXL2] 1Parser start! Parser Id:{}", m_parserId.c_str());
    // �����߳�

//    if(m_transWorker == NULL){
//        m_transWorker.reset(new StdThread([this, contractSet](){
//            if(m_transCpuCore > 0){
//                cpu_set_t cpu_mask;
//                CPU_ZERO(&cpu_mask);
//                CPU_SET(m_transCpuCore, &cpu_mask);
//                // ���ö�Ӧ���̺߳���
//                int ret = pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpu_mask);
//                if (ret != 0) {
//                    printf("[ParserHuaXL2] Parser Run Worker thread affinity failed! ret:%d\n", ret);
//                } else {
//                    printf("[ParserHuaXL2] Parser Run Worker thread affinity success! cpu core:%d\n", m_transCpuCore);
//                }
//            }
//            subscribe(contractSet);
//            connect();
//            while (true)
//            {
//                std::this_thread::sleep_for(std::chrono::nanoseconds(1));
//                // m_asyncio.run_one();
//            }
//        }));
//    }
    return true;
}

bool ParserHuaXL2::disconnect()
{
	if (mp_api)
	{
        mp_api->RegisterSpi(NULL);
        mp_api->Release();
        mp_api = NULL;
	}
    m_isConnected = false;
	return true;
}

void ParserHuaXL2::DoLogin()
{
	if (mp_api == NULL)
	{
		return;
	}
    CTORATstpReqUserLoginField req_user_login_field;
	memset(&req_user_login_field, 0, sizeof(req_user_login_field));

//	strcpy(req_user_login_field.UserProductInfo, "WonderTrader");
//
//    strcpy(req_user_login_field.LogInAccount, _strUser.c_str());
//    req_user_login_field.LogInAccountType = TORA_TSTP_LACT_UserID;
//    strcpy(req_user_login_field.Password, m_strPass.c_str());

	int iResult = mp_api->ReqUserLogin(&req_user_login_field, ++m_iRequestID);

	if (iResult != 0)
	{
        // logi("ParserHuaXL2::DoLogin iResult[{}]",iResult);
	}
	else
	{
        m_loginState = LS_LOGINING;
	}
}

void ParserHuaXL2::DoSubscribeMD()
{
	CodeSet codeFilter = m_fitSHSubs;
	if (!codeFilter.empty() && (CONNECT_MARKET_SH == m_market || CONNECT_MARKET_SHSZ == m_market))
	{
		char** subscribe = new char* [codeFilter.size()];
//		int nCount = 1;
//        int nCount = 0;
//        CodeSet::iterator it = codeFilter.begin();
//        for (; it != codeFilter.end(); it++)
//        {
//            std::string code = *it;
//            subscribe[nCount++] = (char*)(*it).c_str();
//        }
        int nCount = 0;
        if(!m_codes.empty()){
            const StringVector& ay = StrUtil::split(m_codes.c_str(), ",");
            if(ay.size() > 0){
                // ѭ������
                for(int i = 0; i < ay.size(); i++){
                    subscribe[nCount++] = (char*) ay[i].c_str();
                    // logi("[ParserHuaXL2]Parser Id {} Sscribe Code {}", m_parserId.c_str(), subscribe[i]);
                }
            }
        }

		if (mp_api)
		{
			doSubscribe(subscribe, nCount, TORA_TSTP_EXD_SSE);
		}
		codeFilter.clear();
		delete[] subscribe;
	}

	codeFilter = m_fitSZSubs;
	if (!codeFilter.empty() && (CONNECT_MARKET_SZ == m_market || CONNECT_MARKET_SHSZ == m_market))
	{
        char** subscribe = new char* [codeFilter.size()];
//        int nCount = 2;
//        subscribe[0] = (char*) "3*****";
//        subscribe[1] = (char*) "0*****";
        int nCount = 0;
        if(!m_codes.empty()){
            const StringVector& ay = StrUtil::split(m_codes.c_str(), ",");
            if(ay.size() > 0){
                // ѭ������
                for(int i = 0; i < ay.size(); i++){
                    subscribe[nCount++] = (char*) ay[i].c_str();
                    // logi("[ParserHuaXL2]Parser Id {} Sscribe Code {}", m_parserId.c_str(), subscribe[i]);
                }
            }
        }
//        int nCount = 0;
//        CodeSet::iterator it = codeFilter.begin();
//        for (; it != codeFilter.end(); it++)
//        {
//            std::string code = *it;
//            subscribe[nCount++] = (char*)(*it).c_str();
//        }
        if (mp_api)
        {
            doSubscribe(subscribe, nCount, TORA_TSTP_EXD_SZSE);
        }
        codeFilter.clear();
        delete[] subscribe;
	}

}

void ParserHuaXL2::doSubscribe(char *ppSecurityID[], int nCount, TTORATstpExchangeIDType ExchageID){
    int iResult = -1;
    // �ж�m_subscribeType�Ƿ����1
    if(m_subscribeType.find(L2_TICK_TYPE) != std::string::npos){
        iResult = mp_api->SubscribeMarketData(ppSecurityID, nCount, ExchageID);
        if (iResult != 0)
        {
            // loge("ParserHuaXL2::Parser Id {} doSubscribe MarketData iResult[{}]", m_parserId.c_str(), iResult);
        }
        else
        {
            // logi("[ParserHuaXL2]Parser Id {} Market data of {} instruments of {} subscribed", m_parserId.c_str(), nCount, ExchageID);
        }
    }
    if(m_subscribeType.find(L2_TRANSACTION_TYPE) != std::string::npos) {
        // ������ʳɽ�
        iResult = mp_api->SubscribeTransaction(ppSecurityID, nCount, ExchageID);
        if (iResult != 0) {
            // loge("[ParserHuaXL2] Parser Id {} Sending transaction subscribe request of {} failed:{}", m_parserId.c_str(), ExchageID, iResult);
        } else {
            // logi("[ParserHuaXL2] Parser Id {} Transaction data of {} instruments of {} subscribed", m_parserId.c_str(), nCount, ExchageID);
        }
    }
    if(m_subscribeType.find(L2_ORDER_TYPE) != std::string::npos) {
        // �������ί��
        iResult = mp_api->SubscribeOrderDetail(ppSecurityID, nCount, ExchageID);
        if (iResult != 0) {
            // loge("[ParserHuaXL2]Parser Id {} Sending order detail subscribe request of {} failed:{}", m_parserId.c_str(), ExchageID, iResult);
        } else {
            // logi("[ParserHuaXL2]Parser Id {} Order detail data of {} instruments of {} subscribed", m_parserId.c_str(), nCount, ExchageID);
        }
    }
    fflush(stdout);
}

/**
 * ������ʳɽ������ί��
 * @param ppSecurityID
 * @param nCount
 * @param ExchageID
 */
int ParserHuaXL2::subscribeTO(char *ppSecurityID[], int nCount, TTORATstpExchangeIDType ExchageID){
    int iResult = -1;
    // ��Ҫ�ж϶����Ϻ��������������ڽ�����
    if(ExchageID == TORA_TSTP_EXD_SSE){
        // �Ͻ���
        iResult = mp_api->SubscribeNGTSTick(ppSecurityID, nCount, ExchageID);
//        if (iResult != 0) {
//            printf("[ParserHuaXL2] SubscribeTO Parser Id %s Sending NGTSTick subscribe request of %d failed:%d\n", m_parserId.c_str(), ExchageID, iResult);
//        } else {
//            printf("[ParserHuaXL2] SubscribeTO Parser Id %s NGTSTick data of %s instruments of %d subscribed\n", m_parserId.c_str(), ppSecurityID[0], ExchageID);
//        }
    } else if(ExchageID == TORA_TSTP_EXD_SZSE){
        // ���
        iResult = mp_api->SubscribeOrderDetail(ppSecurityID, nCount, ExchageID);
//    if (iResult != 0) {
//
//        printf("[ParserHuaXL2] SubscribeTO Parser Id %s Sending order detail subscribe request of %d failed:%d\n", m_parserId.c_str(), ExchageID, iResult);
//    } else {
//        printf("[ParserHuaXL2] SubscribeTO Parser Id %s Order detail data of %s instruments of %d subscribed\n", m_parserId.c_str(), ppSecurityID[0], ExchageID);
//    }
        // ������ʳɽ�
        iResult = mp_api->SubscribeTransaction(ppSecurityID, nCount, ExchageID);
//    if (iResult != 0) {
//        printf("[ParserHuaXL2] SubscribeTO Parser Id %s Sending transaction subscribe request of %d failed:%d\n", m_parserId.c_str(), ExchageID, iResult);
//    } else {
//        printf("[ParserHuaXL2] SubscribeTO Parser Id %s Transaction data of %s instruments of %d subscribed\n", m_parserId.c_str(), ppSecurityID[0], ExchageID);
//    }

//    fflush(stdout);
    }


    return iResult;
}

/**
 * ������ʳɽ������ί��
 * @param ppSecurityID
 * @param nCount
 * @param ExchageID
 */
int ParserHuaXL2::unSubscribeTO(char *ppSecurityID[], int nCount, TTORATstpExchangeIDType ExchageID){
    int iResult = -1;
    // ��Ҫ�ж϶����Ϻ��������������ڽ�����
    if(ExchageID == TORA_TSTP_EXD_SSE){
        // �Ͻ���
        iResult = mp_api->UnSubscribeNGTSTick(ppSecurityID, nCount, ExchageID);
    } else if(ExchageID == TORA_TSTP_EXD_SZSE){
        // ������ʳɽ�
        iResult = mp_api->UnSubscribeTransaction(ppSecurityID, nCount, ExchageID);
//    if (iResult != 0) {
//        printf("[ParserHuaXL2] UnSubscribeTO Parser Id %s Sending transaction UnSubscribe request of %d failed:%d\n", m_parserId.c_str(), ExchageID, iResult);
//    } else {
//        printf("[ParserHuaXL2] UnSubscribeTO Parser Id %s Transaction data of %s instruments of %d UnSubscribe\n", m_parserId.c_str(), ppSecurityID[0], ExchageID);
//    }
        iResult = mp_api->UnSubscribeOrderDetail(ppSecurityID, nCount, ExchageID);
//    if (iResult != 0) {
//
//        printf("[ParserHuaXL2] UnSubscribeTO Parser Id %s Sending order detail UnSubscribe request of %d failed:%d\n", m_parserId.c_str(), ExchageID, iResult);
//    } else {
//        printf("[ParserHuaXL2] UnSubscribeTOParser Id %s Order detail data of %s instruments of %d UnSubscribe\n", m_parserId.c_str(), ppSecurityID[0], ExchageID);
//    }
//    fflush(stdout);
    }
    return iResult;
}


void ParserHuaXL2::subscribe(const CodeSet& vecSymbols)
{
    for (auto& code : vecSymbols)
    {
        if (strncmp(code.c_str(), "SSE.", 4) == 0)
        {
            m_fitSHSubs.insert(code.c_str() + 4);
            if (CONNECT_MARKET_SH == m_market || CONNECT_MARKET_SHSZ == m_market){
                _mapContracts.insert(std::make_pair(code.c_str() + 4, code.c_str()));
            }
        }
        else if (strncmp(code.c_str(), "SZSE.", 5) == 0)
        {
            m_fitSZSubs.insert(code.c_str() + 5);
            if (CONNECT_MARKET_SZ == m_market || CONNECT_MARKET_SHSZ == m_market) {
                _mapContracts.insert(std::make_pair(code.c_str() + 5, code.c_str()));
            }
        }
    }

    // logi("[ParserHuaXL2] Load SSE Subscribed StdCode Num:{}, SZSE Subscribed StdCode Num:{}, Total Subscribed StdCode Num:{}", m_fitSHSubs.size(), m_fitSZSubs.size(), _mapContracts.size());
    connect();
}

std::string ParserHuaXL2::getContractCode(const char* code){
    auto it = _mapContracts.find(code);
    if(it == _mapContracts.end()){
        return "";
    }
    return it->second;
}

void ParserHuaXL2::unsubscribe(const CodeSet& vecSymbols)
{
}

bool ParserHuaXL2::isConnected()
{
	return m_loginState == LS_LOGINED;
}

void ParserHuaXL2::registerSpi(PsiBaseDataMgr *baseDataMgr, PsiDataMgr *dataMgr, PsiParserDataMgr *parserDataMgr, const char* parserId, int computeCoreSize)
{
    if(NULL != baseDataMgr){
        mp_baseDataMgr = baseDataMgr;
    }
    if(NULL != dataMgr){
        mp_dataMgr = dataMgr;
    }
    if(NULL != parserDataMgr){
        mp_parserDataMgr = parserDataMgr;
    }
    m_computeCoreSize = computeCoreSize;
    m_parserId = parserId;
    // logi("ParserHuaXL2::registerSpi m_parserId[{}] computeCoreSize[{}]",m_parserId.c_str(),computeCoreSize);
    fflush(stdout);
}

void ParserHuaXL2::OnFrontConnected()
{
    // logi("ParserHuaXL2::OnFrontConnected");

	DoLogin();
}

void ParserHuaXL2::OnFrontDisconnected(int nReason)
{
    // loge("ParserHuaXL2::OnFrontDisconnected nReason[{}]",nReason);
    m_loginState = LS_NOTLOGIN;
}

void ParserHuaXL2::OnRspError(CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo->ErrorID != 0)
    {
        // loge("ParserHuaXL2::OnRspError data failed: {}:{}",pRspInfo->ErrorID,pRspInfo->ErrorMsg);
        m_loginState = LS_NOTLOGIN;
    }
}

void ParserHuaXL2::OnRspUserLogout(CTORATstpUserLogoutField *pUserLogout, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo->ErrorID != 0)
    {
        // loge("ParserHuaXL2::OnRspUserLogout data failed: {}:{}",pRspInfo->ErrorID,pRspInfo->ErrorMsg);
        m_loginState = LS_NOTLOGIN;
    }
}

void ParserHuaXL2::OnRspUserLogin(CTORATstpRspUserLoginField *pRspUserLogin, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	if (pRspInfo->ErrorID == 0)
	{
        m_loginState = LS_LOGINED;
		_uTradingDate = TimeUtils::getCurDate();

		DoSubscribeMD();
	}
	else
	{
        // loge("ParserHuaXL2::OnRspUserLogin data failed: {}:{}",pRspInfo->ErrorID,pRspInfo->ErrorMsg);
        m_loginState = LS_NOTLOGIN;
	}
}

// ���Ŀ�������Ӧ��
void ParserHuaXL2::OnRspSubMarketData(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
	if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
	{
        // logi("ParserHuaXL2::OnRspSubMarketData SecurityID[{}] ExchangeID[{}] Success!", pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
        m_loginState = LS_NOTLOGIN;
	}
}

// ȡ����������Ӧ��
void ParserHuaXL2::OnRspUnSubMarketData(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo->ErrorID != 0)
    {
        printf("ParserHuaXL2::OnRspUnSubMarketData data failed: %d:%s\n",pRspInfo->ErrorID,pRspInfo->ErrorMsg);
        m_loginState = LS_NOTLOGIN;
    }

};

// ����ָ������Ӧ��
void ParserHuaXL2::OnRspSubIndex(TORALEV2API::CTORATstpSpecificSecurityField* pSpecificSecurity, TORALEV2API::CTORATstpRspInfoField* pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {

    }
}

// ������ʳɽ�����Ӧ��
void ParserHuaXL2::OnRspSubTransaction(CTORATstpSpecificSecurityField* pSpecificSecurity, CTORATstpRspInfoField* pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        // printf("[ParserHuaXL2] OnRspSubTransaction SecurityID[%s] ExchangeID[%d] Success!\n",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);

    }
}

// ȡ��������ʳɽ�Ӧ��(���ڷ�ծȯ�ࡢ���ڿ�תծ)
void ParserHuaXL2::OnRspUnSubTransaction(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast){
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        // printf("[ParserHuaXL2] OnRspUnSubTransaction SecurityID[%s] ExchangeID[%d] Success!\n",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
}

//�������ί������Ӧ��
void ParserHuaXL2::OnRspSubOrderDetail(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        // printf("[ParserHuaXL2] OnRspSubOrderDetail SecurityID[%s] ExchangeID[%d] Success!\n",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
}

// ȡ���������ί��Ӧ��(���ڷ�ծȯ�ࡢ���ڿ�תծ)
void ParserHuaXL2::OnRspUnSubOrderDetail(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast){
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        // printf("[ParserHuaXL2] OnRspUnSubOrderDetail SecurityID[%s] ExchangeID[%d] Success!\n",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
}

///�����Ϻ�NGTS��ծȯ�������Ӧ��
void ParserHuaXL2::OnRspSubNGTSTick(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast){
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        //printf("[ParserHuaXL2] OnRspSubNGTSTick SecurityID[%s] ExchangeID[%d] Success!\n",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
}

///ȡ�������Ϻ�NGTS��ծȯ�������Ӧ��
void ParserHuaXL2::OnRspUnSubNGTSTick(CTORATstpSpecificSecurityField *pSpecificSecurity, CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast){
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        //printf("[ParserHuaXL2] OnRspUnSubNGTSTick SecurityID[%s] ExchangeID[%d] Success!\n",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
}

//������ծ�������Ӧ��
void ParserHuaXL2::OnRspSubXTSTick(CTORATstpSpecificSecurityField* pSpecificSecurity, CTORATstpRspInfoField* pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        // logi("[ParserHuaXL2] OnRspSubXTSTick SecurityID[{}] ExchangeID[{}] Success!",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
}

//�����Ϻ�XTSծȯ����Ӧ��
void ParserHuaXL2::OnRspSubXTSMarketData(CTORATstpSpecificSecurityField *pSpecificSecurity,
                                         CTORATstpRspInfoField *pRspInfo, int nRequestID, bool bIsLast)
{
    if (pRspInfo && pRspInfo->ErrorID == 0 && pSpecificSecurity)
    {
        // logi("[ParserHuaXL2] OnRspSubXTSMarketData SecurityID[{}] ExchangeID[{}] Success!",pSpecificSecurity->SecurityID, pSpecificSecurity->ExchangeID);
    }
    if(bIsLast)
    {
        // loge("OnRspSubXTSMarketData::nRequestID[{}]",nRequestID);
    }
}

void ParserHuaXL2::OnRtnMarketData(CTORATstpLev2MarketDataField *pDepthMarketData, const int FirstLevelBuyNum, const int FirstLevelBuyOrderVolumes[], const int FirstLevelSellNum, const int FirstLevelSellOrderVolumes[])
{
    int _lev2MarketDataIndex = mp_parserDataMgr->m_lev2MarketDataIndex;
    memcpy(&mp_parserDataMgr->m_lev2MarketDataField[_lev2MarketDataIndex], pDepthMarketData, sizeof(CTORATstpLev2MarketDataField));
    mp_parserDataMgr->m_lev2MarketDataSubs[_lev2MarketDataIndex].rec_time_ns = mp_baseDataMgr->getCurNowTime();
    mp_parserDataMgr->m_lev2MarketDataIndex++;
    return;
}

// ָ����������֪ͨ
void ParserHuaXL2::OnRtnIndex(CTORATstpLev2IndexField* pIndex)
{
    printf("OnRtnIndex SecurityID[%s] ", pIndex->SecurityID);
    printf("ExchangeID[%d] ", pIndex->ExchangeID);
    printf("DataTimeStamp[%d] ", pIndex->DataTimeStamp);//��ȷ���룬�Ϻ�ָ��5��һ�ʣ�����3��һ��
    printf("LastIndex[%.2f] ", pIndex->LastIndex);
    printf("PreCloseIndex[%.2f] ", pIndex->PreCloseIndex);
    printf("OpenIndex[%.2f] ", pIndex->OpenIndex);
    printf("LowIndex[%.2f] ", pIndex->LowIndex);
    printf("HighIndex[%.2f] ", pIndex->HighIndex);
    printf("CloseIndex[%.2f] ", pIndex->CloseIndex);
    printf("Turnover[%.2f] ", pIndex->Turnover);
    printf("TotalVolumeTraded[%lld]\n", pIndex->TotalVolumeTraded);
}

//��ʳɽ�֪ͨ
void ParserHuaXL2::OnRtnTransaction(CTORATstpLev2TransactionField *pTransaction)
{
    int _transactionIndex = mp_parserDataMgr->m_transactionIndex;
    memcpy(&mp_parserDataMgr->m_transactionFields[_transactionIndex], pTransaction, sizeof(CTORATstpLev2TransactionField));
    // mp_parserDataMgr->m_transDetailSubs[_transactionIndex].rec_time = mp_baseDataMgr->getNowTime();
    // mp_parserDataMgr->m_transDetailSubs[_transactionIndex].rec_time_ns = mp_baseDataMgr->getCurNowTime();
    mp_parserDataMgr->m_transactionIndex++;
}

//���ί��֪ͨ
void ParserHuaXL2::OnRtnOrderDetail(CTORATstpLev2OrderDetailField * pOrderDetail)
{
    int _orderDetailIndex = mp_parserDataMgr->m_orderDetailIndex;
    memcpy(&mp_parserDataMgr->m_orderDetailFields[_orderDetailIndex], pOrderDetail, sizeof(CTORATstpLev2OrderDetailField));
    // mp_parserDataMgr->m_orderDetailSubs[_orderDetailIndex].rec_time = mp_baseDataMgr->getNowTime();
    // mp_parserDataMgr->m_orderDetailSubs[_orderDetailIndex].rec_time_ns = mp_baseDataMgr->getCurNowTime();
    mp_parserDataMgr->m_orderDetailIndex++;
}

void ParserHuaXL2::OnRtnNGTSTick(CTORATstpLev2NGTSTickField *pTick){
//    printf("OnRtnNGTSTick SecurityID[%s] ", pTick->SecurityID);
//    printf("ExchangeID[%c] ", pTick->ExchangeID);
//    printf("MainSeq[%d] ", pTick->MainSeq);
//    printf("SubSeq[%lld] ", pTick->SubSeq);
//    printf("TickTime[%d] ", pTick->TickTime);
//    printf("TickType[%c] ", pTick->TickType);
//    printf("BuyNo[%lld] ", pTick->BuyNo);
//    printf("SellNo[%lld] ", pTick->SellNo);
//    printf("Price[%.2f] ", pTick->Price);
//    printf("Volume[%lld] ", pTick->Volume);
//    printf("TradeMoney[%.2f] ", pTick->TradeMoney);
//    printf("Side[%c] ", pTick->Side);
//    printf("TradeBSFlag[%d] ", pTick->TradeBSFlag);
//    printf("Info1[%d] ", pTick->Info1);
//    printf("Info2[%d] ", pTick->Info2);
//    printf("Info3[%d] \n", pTick->Info3);
//    fflush(stdout);
//    int64_t startTime = mp_baseDataMgr->getNowTime();
//    int64_t endTime = mp_baseDataMgr->getNowTime();
//    int64_t time = endTime - startTime;
//    m_transTotalTime += time;
//    m_transTimeIndex++;
//    if (m_transTimeIndex % PARSER_LOG_LENGTH == 0) {
//        printf("[ParserHuaXL2] OnRtnNGTSTick Parser[%s] SecurityID[%s] TotalTime[%ld]\n",
//               m_parserId.c_str(), pTick->SecurityID, m_transTotalTime);
//        fflush(stdout);
//        m_transTotalTime = 0;
//    }
}

//�Ϻ�XTSծȯ��������֪ͨ
void ParserHuaXL2::OnRtnXTSMarketData(CTORATstpLev2XTSMarketDataField *pMarketData,
                                const int FirstLevelBuyNum, const int FirstLevelBuyOrderVolumes[],
                                const int FirstLevelSellNum, const int FirstLevelSellOrderVolumes[])
{

}

//�Ϻ�XTSծȯ�������֪ͨ
void ParserHuaXL2::OnRtnXTSTick(CTORATstpLev2XTSTickField *pTick)
{

}
