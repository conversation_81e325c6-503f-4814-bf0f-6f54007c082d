#pragma once
#include <string>
#include <array>
#include<iostream>
#include <winsock2.h>
#include <ws2tcpip.h>
#define close_socket closesocket
#include <PsiTcpShmClientCommon.h>
#include <cstring>  // 必须包含的头文件

#include <io.h>
#define F_SETFL 4      // Windows等效值
#define O_NONBLOCK 1   // Windows非阻塞标志
// #include <netinet/tcp.h>
// #include <arpa/inet.h>

NS_PSI_TCP_SHM_BEGIN
template<class Derived>
TcpShmClient<Derived>::TcpShmClient(const std::string& ptcp_dir,const std::string& client_name,const ClientConf& client_conf)
    : ptcp_dir_(ptcp_dir),conf_(client_conf),conn_(Connection(conf_)) {
    strncpy(client_name_, client_name.c_str(), sizeof(client_name_) - 1);
    client_name_[sizeof(client_name_) - 1] = 0;
    conn_.init(ptcp_dir.c_str(), client_name.c_str());
}

template<class Derived>
TcpShmClient<Derived>::~TcpShmClient() {
    Stop();
}

template<class Derived>
bool TcpShmClient<Derived>::Connect(bool use_shm,
                                         const char* server_ipv4,
                                         uint16_t server_port,
                                         const CommonConf::LoginUserData& login_user_data) {
    constexpr bool ToLittleEndian = CommonConf::ToLittleEndian;
    if(!conn_.IsClosed()) {
        static_cast<Derived*>(this)->OnSystemError("already connected", 0);
        return false;
    }
    conn_.TryCloseFd();
    const char* error_msg;
    if(!server_name_) {
        std::string last_server_name_file = std::string(ptcp_dir_) + "/" + client_name_ + ".lastserver";
        server_name_ = (char*)MyMmap<ServerName>(last_server_name_file.c_str(), false, &error_msg);
        std::cerr << "last_server_name_file: " << last_server_name_file << std::endl;
        std::cerr << "server_name_: " << (server_name_ ? server_name_ : "(null)") << std::endl;
        if(!server_name_) {
            static_cast<Derived*>(this)->OnSystemError(error_msg, errno);
            return false;
        }
        strncpy(conn_.GetRemoteName(), server_name_, sizeof(ServerName));
    }
    MsgHeader sendbuf[1 + (sizeof(LoginMsg) + 7) / 8];
    sendbuf[0].size = sizeof(MsgHeader) + sizeof(LoginMsg);
    sendbuf[0].msg_type = LoginMsg::msg_type;
    sendbuf[0].ack_seq = 0;
    auto* login = (LoginMsg*)(sendbuf + 1);
    strncpy(login->client_name, client_name_, sizeof(login->client_name));
    strncpy(login->last_server_name, server_name_, sizeof(login->last_server_name));
    login->use_shm = use_shm;
    login->client_seq_start = login->client_seq_end = 0;
    login->user_data = login_user_data;
    if(server_name_[0] &&
        (!conn_.OpenFile(use_shm, &error_msg) ||
        !conn_.GetSeq(&sendbuf[0].ack_seq, &login->client_seq_start, &login->client_seq_end, &error_msg))) {//
        static_cast<Derived*>(this)->OnSystemError(error_msg, errno);
        return false;
    }
    #ifdef _WIN32
        SOCKET fd = INVALID_SOCKET;
    #else
        int fd = -1;
    #endif
    if((fd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("socket", errno);
        return false;
    }
    struct timeval timeout;
    timeout.tv_sec = 10;
    timeout.tv_usec = 0;

    if(setsockopt(fd, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("setsockopt SO_RCVTIMEO", errno);
        close_socket(fd);
        return false;
    }

    if(setsockopt(fd, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("setsockopt SO_RCVTIMEO", errno);
        close_socket(fd);
        return false;
    }
    int yes = 1;
    if( conf_.TcpNoDelay ) {
        int yes = 1;
        const char* optval = reinterpret_cast<const char*>(&yes);
        if(setsockopt(fd, IPPROTO_TCP, TCP_NODELAY, optval, sizeof(yes)) < 0 )
        {
            static_cast<Derived*>(this)->OnSystemError("setsockopt TCP_NODELAY", errno);
            close_socket(fd);
            return false;
        }
    }

    struct sockaddr_in server_addr;
    server_addr.sin_family = AF_INET;
    inet_pton(AF_INET, server_ipv4, &(server_addr.sin_addr));
    server_addr.sin_port = htons(server_port);
    memset(&(server_addr.sin_zero), 0, 8);

    if(connect(fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        static_cast<Derived*>(this)->OnSystemError("connect", errno);
        close_socket(fd);
        return false;
    }

    sendbuf[0].ConvertByteOrder<ToLittleEndian>();
    sendbuf[0].ack_seq = 0;
    login->ConvertByteOrder();
    int ret = send(fd, reinterpret_cast<const char*>(sendbuf), sizeof(sendbuf), 0);
    if(ret != sizeof(sendbuf)) {
        static_cast<Derived*>(this)->OnSystemError("send", ret < 0 ? errno : 0);
        close_socket(fd);
        return false;
    }

    MsgHeader recvbuf[1 + (sizeof(LoginRspMsg) + 7) / 8];
    ret = recv(fd, reinterpret_cast<char*>(recvbuf), sizeof(recvbuf), 0);
    if(ret != sizeof(recvbuf)) {
        static_cast<Derived*>(this)->OnSystemError("recv", ret < 0 ? errno : 0);
        close_socket(fd);
        return false;
    }
    auto* login_rsp = (LoginRspMsg*)(recvbuf + 1);
    recvbuf[0].ConvertByteOrder<ToLittleEndian>();
    login_rsp->ConvertByteOrder();
    if(recvbuf[0].size != sizeof(MsgHeader) + sizeof(LoginRspMsg) || recvbuf[0].msg_type != LoginRspMsg::msg_type ||
        login_rsp->server_name[0] == 0) {
        static_cast<Derived*>(this)->OnSystemError("Invalid LoginRsp", 0);
        close_socket(fd);
        return false;
    }
    if(login_rsp->status != 0) {
        if(login_rsp->status == 1) { // seq number mismatch
            sendbuf[0].ConvertByteOrder<ToLittleEndian>();
            login->ConvertByteOrder();
            static_cast<Derived*>(this)->OnSeqNumberMismatch(
                                                                sendbuf[0].ack_seq,
                                                                login->client_seq_start,
                                                                login->client_seq_end,
                                                                recvbuf[0].ack_seq,
                                                                login_rsp->server_seq_start,
                                                                login_rsp->server_seq_end);
        }
        else {
            static_cast<Derived*>(this)->OnLoginReject(login_rsp);
        }
        close_socket(fd);
        return false;
    }
    login_rsp->server_name[sizeof(login_rsp->server_name) - 1] = 0;
    // check if server name has changed
    if(strncmp(server_name_, login_rsp->server_name, sizeof(ServerName)) != 0) {
        conn_.Release();
        strncpy(server_name_, login_rsp->server_name, sizeof(ServerName));
        strncpy(conn_.GetRemoteName(), server_name_, sizeof(ServerName));
        if(!conn_.OpenFile(use_shm, &error_msg)) {
            static_cast<Derived*>(this)->OnSystemError(error_msg, errno);
            close_socket(fd);
            return false;
        }
        conn_.Reset();
    }

// 跨平台非阻塞设置
#ifdef _WIN32
    u_long nonblocking = 1;
    if (ioctlsocket(fd, FIONBIO, &nonblocking) == SOCKET_ERROR) {
        static_cast<Derived*>(this)->OnSystemError("ioctlsocket FIONBIO", WSAGetLastError());
        close_socket(fd);
        return false;
    }
#else
    int flags = fcntl(fd, F_GETFL, 0);
    if (flags == -1) {
        static_cast<Derived*>(this)->OnSystemError("fcntl F_GETFL", errno);
        close_socket(fd);
        return false;
    }
    if (fcntl(fd, F_SETFL, flags | O_NONBLOCK) == -1) {
        static_cast<Derived*>(this)->OnSystemError("fcntl F_SETFL", errno);
        close_socket(fd);
        return false;
    }
#endif

    int64_t now = static_cast<Derived*>(this)->OnLoginSuccess(login_rsp);

// 添加类型转换(Windows需要)
#ifdef _WIN32
    conn_.Open(static_cast<int>(fd), recvbuf[0].ack_seq, now);
#else
    conn_.Open(fd, recvbuf[0].ack_seq, now);
#endif
    return true;
}

template<class Derived>
void TcpShmClient<Derived>::PollTcp(int64_t now) {
    // 添加安全保护：在访问前检查连接状态
    if(!conn_.IsClosed())
    {
        MsgHeader* head = nullptr;

        // 使用安全访问模式
        try {
            head = conn_.TcpFront(now);
        } catch (const std::exception& e) {
            // 处理异常
            conn_.Close();
            static_cast<Derived*>(this)->OnDisconnected(e.what(), 0);
            return;
        }
        // std::cout << "head: " << head << std::endl;
        if (head) {
            try {
                static_cast<Derived*>(this)->OnServerMsg(head);
            } catch (const std::exception& e) {
                // 处理消息处理中的异常
                conn_.Close();
                static_cast<Derived*>(this)->OnDisconnected(e.what(), 0);
            }
        }
    }
    // 安全关闭文件描述符
    if (conn_.TryCloseFd()) {
        int sys_errno = 0;
        const char* reason = "File descriptor closed";

        // 获取关闭原因（如果有）
        try {
            reason = conn_.GetCloseReason(&sys_errno);
        } catch (...) {
            // 忽略获取原因时的错误
        }

        static_cast<Derived*>(this)->OnDisconnected(reason, sys_errno);
    }
}

template<class Derived>
void TcpShmClient<Derived>::PollShm() {
    MsgHeader* head = conn_.ShmFront();
    if(head) static_cast<Derived*>(this)->OnServerMsg(head);
}

template<class Derived>
void TcpShmClient<Derived>::Stop() {
    if(server_name_) {
        MyUnmap<ServerName>(server_name_);
        server_name_ = nullptr;
    }
    conn_.Release();
}

template<class Derived>
auto TcpShmClient<Derived>::GetConnection()->Connection& {
    return conn_;
}

NS_PSI_TCP_SHM_END
