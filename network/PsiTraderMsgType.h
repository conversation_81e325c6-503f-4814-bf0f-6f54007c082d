//
// Created by Fsyrl on 24-9-27.
//

#ifndef PSIMSGTYPE_H
#define PSIMSGTYPE_H
#include "PsiStruct.h"
#include "PsiTcpShmServerCommon.h"
#include "ThostFtdcTraderApi.h"
// #include "PsiRunner.h"
using namespace tcpshm;

enum MsgType {
    MSG_RECEIVED_TYPE = 1,
    MSG_PING_TYPE,
    MSG_CMD_TYPE,
    MSG_SUB_TYPE,
    MSG_UNSUB_TYPE,
    MSG_MDL_MESSAGE_TYPE,
    MSG_TRADER_TYPE,
    MSG_USER_LOGIN_TYPE,
    MSG_USER_LOGOUT_TYPE,
    MSG_SETTLEMENT_INFO_CONFORM_TYPE,
    MSG_QRY_TRADINGACCOUNT_TYPE,
    MSG_ORDER_INSERT_TYPE,
    MSG_ORDER_ACTION_TYPE,
    MSG_QRY_INVESTORPOSITION_TYPE,
    MSG_QRY_SETTLEMENT_INFO_TYPE,
    MSG_QRY_TRADE_TYPE,
    MSG_QRY_ORDER_TYPE,
};

//需要保证固定大小，以便于传输
//如果需要不固定的，请自行memset构造

#pragma pack(push, 1)  // 强制1字节对齐，避免填充

struct BaseHeader {
    uint16_t token_len; // 令牌长度
    char token[40]; // 用户认证令牌 - 增加到40字节以容纳36字符+终止符
};

struct PingReq : MsgTpl<MSG_PING_TYPE>, BaseHeader {
    uint32_t seq_no;//消息序列号
    uint64_t send_time;//发送时间戳-纳秒级
    char val[8]; // 增加到8字节以容纳"ping"+终止符
};

struct PingResp : MsgTpl<MSG_PING_TYPE> {
    uint32_t seq_no;//消息序列号
    uint64_t ack_time;//确认时间戳
    char val[8]; // 增加到8字节以容纳"pong"+终止符
}; //接收的结构体

struct strategyGroupPageListReq {
    std::string order;
    std::string cmd;
    int sort;
    int pageNo;
    int pageSize;
};

struct cmdReq : MsgTpl<MSG_CMD_TYPE> {
    uint32_t seq_no;
    uint16_t token_len; // 令牌长度
    char token[40]; // 用户认证令牌 - 增加到40字节
    uint16_t cmd_len; // 命令长度
    char cmd[64]; // 命令内容
    uint32_t data_len;
    // 数据长度
    // char data[0]; // 可变长度数据
}; //发送的结构体

struct cmdResp : MsgTpl<MSG_CMD_TYPE> {
    uint32_t seq_no;
    uint16_t cmd_len; // 命令长度
    char cmd[64]; // 命令内容
    uint16_t account_id_len; // 账户ID长度
    char account_id[32]; // 账户ID
    uint16_t user_id_len;
    char user_id[32]; // 用户ID
    uint16_t data_len; // 数据长度
    char data[0]; // 可变长度数据
}; //接收的结构体

struct ReceiveResp : MsgTpl<MSG_RECEIVED_TYPE> {
};

//
struct TraderReq : MsgTpl<MSG_TRADER_TYPE> {
    char clientId[10];
    char exchangeId[2];
    PSIDoTraderStruct _doTrader;
}; //发送的结构体

struct UserLoginResp : MsgTpl<MSG_USER_LOGIN_TYPE> {
    CThostFtdcRspUserLoginField userLogin;
};

struct UserLogoutResp : MsgTpl<MSG_USER_LOGOUT_TYPE> {
    CThostFtdcUserLogoutField userLogout;
};

struct SettlementInfoConfirmResp : MsgTpl<MSG_SETTLEMENT_INFO_CONFORM_TYPE> {
    CThostFtdcSettlementInfoConfirmField settlementInfo;
};

struct QryTradingAccountResp : MsgTpl<MSG_QRY_TRADINGACCOUNT_TYPE> {
     CThostFtdcTradingAccountField tradingAccount;
};

struct OrderInsertResp : MsgTpl<MSG_ORDER_INSERT_TYPE> {
    CThostFtdcInputOrderField orderInsert;
};

struct OrderActionResp : MsgTpl<MSG_ORDER_ACTION_TYPE> {
    CThostFtdcInputOrderActionField orderAction;
};

struct QryInvestorPositionResp : MsgTpl<MSG_QRY_INVESTORPOSITION_TYPE> {
     CThostFtdcInvestorPositionField investorPosition;
};

struct QrySettlementInfoResp : MsgTpl<MSG_QRY_SETTLEMENT_INFO_TYPE> {
    CThostFtdcSettlementInfoField qrySettlementInfo;
};

struct QryTradeResp : MsgTpl<MSG_QRY_TRADE_TYPE> {
     CThostFtdcTradeField qryTrade;
};

struct QryOrderResp : MsgTpl<MSG_QRY_ORDER_TYPE> {
     CThostFtdcOrderField qryOrder;
};

#pragma pack(pop)  // 恢复默认对齐

#endif //PSIMSGTYPE_H
