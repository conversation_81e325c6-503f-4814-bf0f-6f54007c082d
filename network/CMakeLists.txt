# network/CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

# 收集所有源文件
file(GLOB_RECURSE PSI_SOURCES
    "*.cpp"
    "*.hpp"
    "*.tpp"
    "*.c"
    "*.h"
)

# 创建网络库
add_library(PsiNetwork STATIC ${PSI_SOURCES})

# 添加包含路径
target_include_directories(PsiNetwork PUBLIC
   ${CMAKE_CURRENT_SOURCE_DIR}
)

# 添加编译定义
target_compile_definitions(PsiNetwork PRIVATE
    BOOST_ALL_NO_LIB
)

set(BOOST_ROOT "D:/Boost")
find_package(Boost 1.88 REQUIRED COMPONENTS filesystem)
include_directories(${Boost_INCLUDE_DIRS})
include_directories("D:/plugin/Rapidjson/rapidjson/include")  # 直接指定绝对路径
include_directories("D:/atomic_queuee/include")  # 直接指定绝对路径
include_directories("D:/plugin/Hiredis/hiredis")  # 直接指定绝对路径
# Windows 特定设置
if(WIN32)
    target_link_libraries(PsiNetwork PRIVATE ws2_32)
endif()
