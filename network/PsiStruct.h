/*!
 * \file PsiStruct.h
 * \project	PsiTraderMagicWeapon
 *
 * \author l<PERSON><PERSON>
 * \date 2024/02/25
 * 
 * \brief 基础结构体定义
 */
#pragma once
#include <memory>
#include <stdint.h>
#include <string.h>
#include <map>
#include "PsiTypes.h"
#include "PsiFasterDefs.h"
#include "chrono"

#ifdef _MSC_VER
#pragma warning(disable:4200)
#endif


#pragma pack(push, 1)

/**
 * 数据汇总 结构体 策略使用
 */
struct PSIStraSummaryStruct
{
    char		exchg[MAX_EXCHANGE_LENGTH];
    char		code[MAX_INSTRUMENT_LENGTH];

    uint32_t	trading_date;		//交易日,如20140327
    uint32_t	action_date;		//自然日期,如20140327
    uint32_t	action_time;		//发生时间,精确到毫秒,如105932000

    int64_t		index;			//成交编号(从1开始,递增1)
    double			price;			//成交价格
    uint32_t		volume;			//成交数量
    int64_t			ask_order_no;		//叫卖序号
    int64_t			bid_order_no;		//叫买序号
    int64_t         rec_time;       //接收时间
    int64_t         trans_time;     //成交时间
    int64_t         queue_num;      //队列数据长度
    int64_t         up_last_price_bid_volume; // 上一次最新价买量
    int64_t         total_ask_volume; // 总卖量
    int64_t         up_limit_ask_volume; // 涨停卖量
    int64_t         up_Limit_bid_volume; // 跌停买量
    std::chrono::time_point<std::chrono::high_resolution_clock> rec_tiker; // 接收时间点
    PSIStraSummaryStruct()
    {
        memset(this, 0, sizeof(PSIStraSummaryStruct));
    }
};

/**
 * 逐笔成交的数据结构体
 */
struct PSITransactionStruct
{
    ///交易所代码
    char	ExchangeID;

    ///证券代码
    char	SecurityID[31];

    ///时间戳
    int	TradeTime;

    ///成交价格
    double	TradePrice;

    ///成交数量
    long long int	TradeVolume;

    ///成交类别（只有深圳行情有效）
    char	ExecType;

    ///主序号
    int	MainSeq;

    ///子序号
    long long int	SubSeq;

    ///买方委托序号
    long long int	BuyNo;

    ///卖方委托序号
    long long int	SellNo;

    ///附加信息1
    int	Info1;

    ///附加信息2
    int	Info2;

    ///附加信息3
    int	Info3;

    ///内外盘标志（只有上海行情有效）
    char	TradeBSFlag;

    ///业务序号（只有上海行情有效）
    long long int	BizIndex;
};

/**
 * 逐笔委托的数据结构体
 */
struct PSIOrderDetailStruct
{
    ///交易所代码
    char	ExchangeID;

    ///证券代码
    char	SecurityID[31];

    ///时间戳
    int	OrderTime;

    ///委托价格
    double	Price;

    ///委托数量
    long long int	Volume;

    ///委托方向
    char	Side;

    ///订单类别（只有深圳行情有效）
    char	OrderType;

    ///主序号
    int	MainSeq;

    ///子序号
    int	SubSeq;

    ///附加信息1
    int	Info1;

    ///附加信息2
    int	Info2;

    ///附加信息3
    int	Info3;

    ///委托序号
    long long int	OrderNO;

    ///订单状态
    char	OrderStatus;

    ///业务序号（只有上海行情有效）
    long long int	BizIndex;
};

// 定义计算后的数据结构
struct PSIStatisticalStruct
{
    char		exchg[MAX_EXCHANGE_LENGTH];
    char		code[MAX_INSTRUMENT_LENGTH];

    uint32_t		trading_date;		//交易日,如20140327
    uint32_t		action_date;		//自然日期,如20140327
    uint32_t		action_time;		//发生时间,精确到毫秒,如105932000

    uint32_t			total_volume;			//累计板上成交量
    uint32_t			board_volume;			//板上成交量
    uint32_t			bid_cancel_volume;			//板上买入撤单量
    uint32_t			ask_cancel_volume;			//板上卖出撤单量
    uint32_t			bid_volume;			//板上买入成交量
    double              bid_amount;			//板上买入成交金额
    uint32_t			ask_volume;			//板上卖出成交量
    uint32_t			bid_order_volume;			//板上委托买入量
    double              bid_order_amount;			//板上委托买入金额
    uint32_t			ask_order_volume;			//板上委托卖出量
    uint32_t			bid_qty_volume;			//板上委买量 （未成交的）
    uint32_t			ask_qty_volume;			//板上委卖量
    uint32_t			pressure_volume;			//板上压单量
    double              pressure_amount;			//板上压单金额
    double              open;				//开盘价
    double              high;				//最高价
    double              low;				//最低价
    double              close;				//收盘价
    uint32_t            volume;				//成交量
    double              amount;				//成交额

    PSIStatisticalStruct()
    {
        memset(this, 0, sizeof(PSIStatisticalStruct));
    }
};

// 定义计算后的数据结构
struct PSIStrategyMappingStruct
{
    char		exchg[MAX_EXCHANGE_LENGTH];
    char		code[MAX_INSTRUMENT_LENGTH];
    char        str_stra_id[MAX_INSTRUMENT_LENGTH]; // 策略ID
    char        str_stra_name[MAX_NAME_LENGTH]; // 策略名称

    uint32_t		stra_id;		//WT策略ID
    bool		    is_active;		// 是否有效 默认无效
    double          params[50];      // 参数值

    PSIStrategyMappingStruct()
    {
        memset(this, 0, sizeof(PSIStrategyMappingStruct));
    }
};

// 定义千档数据结构(买卖委托订单量)
struct PSIOrderBookStruct
{
    char		exchg[MAX_EXCHANGE_LENGTH];
    char		code[MAX_INSTRUMENT_LENGTH];

    uint32_t	bid_order_volume;			//买单量
    uint32_t	ask_order_volume;			//卖单量

    PSIOrderBookStruct()
    {
        memset(this, 0, sizeof(PSIOrderBookStruct));
    }
};

// 股票封板开板信息
struct PSIStockSealingPlateStruct
{
    uint32_t	time;			//买单量
    uint32_t	status;			//1:封板 2:开板

    PSIStockSealingPlateStruct()
    {
        memset(this, 0, sizeof(PSIOrderBookStruct));
    }
};

// 千档委托结构体
struct PSIThousandLevelOrdersInfoStruct
{
    int64_t*	bid_order_volumes;			// 存储千档委托的委托买入量
    int64_t*	ask_order_volumes;			// 存储千档委托的委托卖出量
    int64_t     up_last_price_bid_volume = 0;    // 存储优于最新价的买入委托总量
    int64_t     bid_order_volume = 0; // 存储所有买入委托总量
    int64_t     ask_order_volume = 0; // 存储所有卖出委托总量
    int64_t     bid_market_order_qty = 0; // 存储市价买入委托总量
    int64_t     ask_market_order_qty = 0; // 存储市价卖出委托总量
    int64_t     action_time = 0; // 存储委托时间
    int64_t     up_limit_bid_trade_qty = 0; // 存储涨停价买入成交量
};

// 行情模块参数数据结构
struct PSIParserParamsStruct
{
    std::string         parser_id; // 行情模块Id
    std::string			sub_mode; // TCP连接模式 0  UDP单播模式 1 UDP组播模式 2
    bool			    cached_mode; // 延伸功能连接模式
    std::string			front;  // tcp://************:9402
    std::string         broker;
    std::string         interface_ip; // 接收网卡地址,如:"127.0.0.1",填 NULL 则依次轮询尝试本机所有网卡加入组播组
    std::string         source_ip; // 组播数据包源地址,如:"127.0.0.1",填 NULL 表示不校验数据包源
    std::string			market; // USH USZ USH;USZ
    std::string         codes; // 当前行情订阅code
    std::string			str_user;
    std::string			str_pass;
    std::string			str_flow_dir;
    std::string			subscribe_type; // 订阅类型 1: TICK 2: ORDER 3: TRANSACTION
    std::string			module;
    bool                proxy; // 是否需要代理
    std::string         cpu_cores; // CPU核心
    int                 order_cpu_core = 0; // 委托线程核
    int                 trans_cpu_core = 0; // 成交线程核
    std::string         interface_name; // 接收网卡名
    int                 rxq_capacity = 0; // 接收网卡索引
    int                 gpsize = 0;
    bool                befvi; // 是否启用网卡索引
};

struct PSIBarStruct
{
    uint32_t	date;		//日期
    uint32_t	tdate;		//交易日期
    uint32_t	reserve_;	//占位符
    uint64_t	time;		//时间
    double		open;		//开
    double		high;		//高
    double		low;		//低
    double		close;		//收
    double		settle;		//结算
    double		amount;		//成交金额
    double		vol;		//成交量
    uint32_t    init;      // 是否初始化过 0:未初始化 1:初始化过
    uint32_t    bclose ;   // 是否闭合
};

// Runner模块参数数据结构
struct PSIRunnerParamsStruct
{
    int	        runner_index;			//runner数量
    std::string	compute_cpu_cores;			//计算线程的核心列表
    std::string stra_cpu_cores;   // 策略线程核心列表
    std::string	parser_dispatch_cpu_cores;			//计算线程的核心列表
    std::string parser_dispatch_l2_cpu_cores; // 处理快照数据核心线程
    std::string parser_dispatch_l1_cpu_cores; // 处理快照数据核心线程
    std::string subscribe_cpu_cores; // 处理订阅核心线程
    std::string market_parsers; // 行情模块Id
    std::string stroke_parsers; // 行情模块Id
    std::string runner_id; // 运行模块Id
};

// Redis模块参数数据结构
struct PSIRedisParamsStruct
{
    std::string     redis_id; // redis模块Id
    std::string		user; // 用户名
    std::string		pass; // 密码
    std::string		front; // tcp://
    std::string     channel; // 订阅频道
    std::string     trader_channel; // 交易订阅频道
    int				port; // 端口
    int             cpu_core = 0;   // 线程核心
    int             trader_cpu_core = 0;   // 线程核心
};

// Redis模块消息推送数据结构
struct PSIRedisPushMsgStruct
{
    char redis_id[256];   // redis模块Id
    char    channel[256]; // 订阅频道
    char key[256]; // key
    char    msg[10240]; // 消息
};

// Redis模块消息推送数据结构
struct PSIRedisRecMsgStruct
{
    char redis_id[256];   // redis模块Id
    char    msg[1024]; // 消息
};

// Runner配置数据结构
struct PSIRunnerConfigStruct
{
    int	        max_runner;			//runner数量
    std::string	thirdparty;			//当前数据读取模式file:文件 redis:缓存
    std::string rediskey;   // redis key
    std::string file_path; // 文件存储位置
    int time_cpu_core = 0;   // 时间线程核心
    int redis_cpu_core = 0;   // redis线程核心
    int redis_trader_cpu_core = 0;   // redis trader线程核心
    int monitor_interval = 0;   // 监控间隔
    int runner_cpu_core = 0; // runner核心
    std::string listing_sector_proportion; // 板块对应开始计算的涨幅比例
    std::string kline_period; // k线类型
    bool compute_kline;
};

// 交易模块参数数据结构
struct PSITraderParamsStruct
{
    uint32_t	    trader_index = 0;			//交易内部ID int
    std::string	    trader_id;			// Java->C++外部使用交易code
    std::string	    sub_trader_id;          // 交易子账户信息
    std::string	    module;			//交易模块
    std::string		user;
    std::string		pass;
    std::string		flowdir;
    bool			encrypt;  // 为网络数据是否加密传输，考虑数据安全性，建议以互联网方式接入的终端设置为加密传输
    std::string		appid; // 终端名称
    std::string     authcode; // 终端授权码
    std::string		sse_share_hold_id; // 沪市股东号
    std::string		szse_share_hold_id; // 深市股东号
    std::string		terminal; // 终端类型，默认PC
    std::string		pub_ip; // 公网IP
    std::string		pub_port; // 公网IP端口号
    std::string		trade_ip; // 内网交易IP
    std::string		mac; // MAC地址，托管服务器网卡MAC
    std::string		hard_disk; // 硬盘序列号,托管服务器硬盘序列号
    uint32_t        cpu_core=0; // 核心数
    uint32_t        trade_cpu_core=0; // 核心数
    uint32_t        query_cpu_core=0; // 核心数
    std::string		front; // 如tcp://************:9500
    std::string     broker;
    std::string		product_info; // 用户端产品信息
    std::string     exch; //交易所列表
    std::string		redis_id; // redis id
    std::string		redis_channel; // redis channel
    std::string     trader_push_channel; // 交易推送channel
    std::string     trader_position_push_channel; // 交易持仓推送channel
    std::string     trader_position_set_key; // 持仓更新key
    std::string     trader_order_set_key; // 下单更新key
    std::string     trader_trans_set_key; // 成交更新key
    int             single_order_limit=100; // 主板单笔订单限制 1表示主板
    int             single_order_limit2=98600; // 科创版单笔订单限制 2表示科创版
    int             single_order_limit3=298600; // 创业板单笔订单限制 3表示创业板
    int             orderef_index;
    int             request_index;
    bool			quick; // 流重传方式
    bool			inited;
    bool            proxy; // 是否需要代理
};

// 日志模块参数数据结构
struct PSILogMessageStruct
{
    char patttern[128]; // 日志模式
    char catName[128];  // 日志分类
    WTSLogLevel ll;    // 日志级别
    char message[256]; // 日志消息
};

// 资源调度模块参数数据结构
struct PSIResourceScheduleParamsStruct
{
    uint32_t	time;			//买单量
    uint32_t	status;			//1:封板 2:开板
};

// // 交易的数据结构
// struct PSIDoTraderParamsStruct
// {
//     std::string	   code;			// 股票代码
//     std::string	   exch;			// 交易所信息
//     std::string    stra_uuid;       // 策略UUID
//     std::string    sys_id;         // 交易订单号 java传送
//     int            stra_id;       // 策略ID
//     char           direction;         // 买卖方向
//     char           order_price_type;         // 交易类型
//     int64_t        volume;        // 交易数量
//     double         price;         // 交易价格
//     char           side;         // 买卖方向 0:买 1:卖 c:撤单
//     int            order_ref; // 报单引用
// };

// 交易的数据结构
struct PSIDoTraderStruct
{
    char	       code[11];			// 股票代码
    char	       exch[9];			// 交易所信息
    char           order_sys_id[64];         // 交易订单号 java传送
    char	       info_str[64];			// 交易所信息
    int            stra_id;       // 策略ID
    char           order_price_type;         // 交易类型
    char           offset_type;          // 开平标志
    char           direction_type;  //买卖方向
    char           order_flag;     // 订单标志
    char           action_flag;    // 动作标志
    int64_t        volume;        // 交易数量
    uint32_t       front_id;      // 前置编号
    uint32_t       session_id;    // 会话编号
    double         price;         // 交易价格
    int            order_ref; // 报单引用

    int            request_id; // 请求编号
    // char           side;     // 委托方向 0:买 1:卖 c:撤单
    // int            seal_board; //几封下的单
    // int            listing_sector; //上市板块 1:主板 2:科创版 3:创业板
    // int            order_no; // 下单时的订单号
};

/**
 * 逐笔委托的数据结构体
 */
struct PSILev2OrderDetailSubStruct
{
    int64_t    rec_time = 0; // 接收时间
    uint64_t   rec_time_ns = 0; // 接收时间
    int64_t    compute_start_time = 0; // 计算时间
    int64_t    compute_end_time = 0; // 计算结束时间
};

/**
 * 逐笔委托的数据结构体
 */
struct PSILev2MaketDataSubStruct
{
    int64_t    rec_time = 0; // 接收时间
    uint64_t   rec_time_ns = 0; // 接收时间
    int64_t    compute_start_time = 0; // 计算时间
    int64_t    compute_end_time = 0; // 计算结束时间
    int64_t    trader_time = 0; // 交易时间
};


/**
 * 逐笔成交的数据结构体
 */
struct PSILev2TransactionSubStruct
{
    int64_t    index = 0; // 成交编号
    int64_t    rec_time = 0; // 接收时间
    uint64_t   rec_time_ns = 0; // 接收时间
    int64_t    compute_start_time = 0; // 计算时间
    int64_t    compute_end_time = 0; // 计算结束时间
};

/**
 * 区间内计算数据结构
 */
struct PSIIntervalCalculateStruct{
    int      start_time = 0; // 时间
    int      end_time = 0; // 时间
    int64_t  board_cancel_volume = 0; // 板上撤单量
    int64_t  board_smash_volume = 0; // 板上砸单量
    int64_t  board_seal_volume = 0; // 板上封单量
};

/**
 * 区间内卖出计算数据结构
 */
struct PSIIntervalCalculateSellStruct{
    int      start_time = 0; // 时间
    int      end_time = 0; // 时间
    double          seals_amount = 0.0; // 封单总金额
    int64_t         seals_volume = 0; // 封单量
    double          up_limit_sell_amount = 0.0; // 板上卖出金额
    int64_t         up_limit_sell_volume = 0; // 板上卖出金额
    double          up_limit_buy_cancel_amount = 0.0; // 板上买入撤单金额
    int64_t         up_limit_buy_cancel_volume = 0; // 板上买入撤单金额
    double          fewer_seals_ratio = 0.0; // 封单减少比例
    int64_t         fewer_seals_value = 0; // 封单减少比例
};

// 交易的查询的数据结构
struct PSIDoTraderQueryStruct
{
    char	    code[11];			// 股票代码
    char	    exch[11];			// 交易所信息
    char        sys_id[64];       // 交易订单号 java传送
    int         query_type=0;       // 查询类型 1:查询证券信息 2:查询持仓信息 3:查询订单信息 4:查询成交信息
};

// 开封板数据结构
struct PSISealOpenBoardStruct
{
    int64_t 	time;			// 开封板时间
    int	        type;  			// 1：开板 2：封板
    int64_t     volume;			// 开板封板板上委托量
    int64_t     order_no;		// 开板封板对应的订单号
};

// 开封板数据结构
struct PSIL2SealOpenBoardStruct
{
    std::string	code;			// 股票代码
    int64_t 	time;			// 开封板时间
    int	        type;  			// 1：封板 2：开板
    int64_t     volume;			// 开板封板板上委托量
};


// 交易信息数据结构
struct PSIDoTraderOrderStruct
{
    char	    code[11];			// 股票代码
    char	    exch[11];			// 交易所信息
    char        stra_uuid[64];       // 策略UUID
    int         stra_id;       // 策略ID
    char        sys_id[64];         // 交易订单号
    char        channel[64];         // 通道
    char        account_id[64];      // 账户ID
    char        order_sys_id[21];    // 交易所订单号
    char        direction;         // 买卖方向
    char        type;         // 交易类型
    char        order_status; // 交易状态 0:下单成功 1:已报单 2:未成交  3:部分成交 4:全部成交 5:部成部撤 6:撤单中 7:已撤单
    char        order_type; // 订单类型
    char        msg[81]; // 交易消息
    char        insert_date[9]; // 下单日期
    char        insert_time[9]; // 下单时间
    char        accept_time[9]; // 交易所接收时间
    char        cancel_time[9]; // 撤销时间
    int64_t     volume;        // 交易数量
    int64_t     trade_volume;        // 成交数量
    int64_t     cancel_volume;        // 撤单数量
    double      trade_amount;         // 成交金额
    double      price;         // 交易价格
    int         order_ref; // 报单引用
    int         request_id; // 请求编号
    int         error_id; // 前置编号
    int     seal_board; //几封下的单
    int     order_no; // 下单时的订单号

    int64_t     time; // 下单时间纳秒时间

};

// 策略配置数据结构
struct PSIStrategyConfigStruct
{
    std::string	thirdparty;			//当前数据读取模式file:文件 redis:缓存
    std::string rediskey;   // redis key 买入
    std::string sellrediskey;   // redis key 卖出
};

// 策略参数数据结构
struct PSIStrategyStruct
{
    uint32_t	stra_id;			// 策略ID
    uint32_t    operator_type;    // 操作类型
    uint32_t    type;    // 类型
    uint32_t    status;    // 状态
    std::string account_id; // 账户ID
    std::string	strategy_code;			// 策略对应code
    std::string stock_pool_key;     // 股票池
    std::string strategy_buy_id;     // 策略32位ID
};

struct PSIStrategyParamsStruct
{
    uint32_t	stra_id;			// 策略ID
    uint32_t    operator_type;    // 操作类型
    uint32_t    type;    // 类型
    uint32_t    status;    // 状态
    int64_t     volume;
    std::string account_id; // 账户ID
    std::string stra_name; // 策略名称
    std::string	strategy_key;			// 策略对应code
    std::string stock_pool_key;     // 股票池
    std::string strategy_buy_id;     // 策略32位ID
    std::string strategy_msg;    // 策略消息
    std::vector<std::string> stock_list; // 股票列表
    std::map<std::string, std::string> params; // 策略参数
    std::map<std::string, bool> params_show; // 策略参数状态信息
};

/// 报单
struct PSIStockOrderStruct
{
    ///交易所代码
    PSIStockExchangeIDType	ExchangeID;

    ///投资者代码
    PSIStockInvestorIDType	InvestorID;

    ///投资单元代码
    PSIStockBusinessUnitIDType	BusinessUnitID;

    ///股东账户代码
    PSIStockShareholderIDType	ShareholderID;

    ///证券代码
    PSIStockSecurityIDType	SecurityID;

    ///买卖方向
    PSIStockDirectionType	Direction;

    ///报单价格条件
    PSIStockOrderPriceTypeType	OrderPriceType;

    ///有效期类型
    PSIStockTimeConditionType	TimeCondition;

    ///成交量类型
    PSIStockVolumeConditionType	VolumeCondition;

    ///价格
    PSIStockPriceType	LimitPrice;

    ///数量
    PSIStockVolumeType	VolumeTotalOriginal;

    ///港股通订单数量类型
    PSIStockLotTypeType	LotType;

    ///有效日期
    PSIStockDateType	GTDate;

    ///委托方式
    PSIStockOperwayType	Operway;

    ///条件检查
    PSIStockCondCheckType	CondCheck;

    ///字符串附加信息
    PSIStockStrInfoType	SInfo;

    ///整形附加信息
    PSIStockIntInfoType	IInfo;

    ///请求编号
    PSIStockRequestIDType	RequestID;

    ///前置编号
    PSIStockFrontIDType	FrontID;

    ///会话编号
    PSIStockSessionIDType	SessionID;

    ///报单引用
    PSIStockOrderRefType	OrderRef;

    ///本地报单编号
    PSIStockOrderLocalIDType	OrderLocalID;

    ///系统报单编号
    PSIStockOrderSysIDType	OrderSysID;

    ///报单状态
    PSIStockOrderStatusType	OrderStatus;

    ///报单提交状态
    PSIStockOrderSubmitStatusType	OrderSubmitStatus;

    ///状态信息
    PSIStockErrorMsgType	StatusMsg;

    ///已成交数量
    PSIStockVolumeType	VolumeTraded;

    ///已撤销数量
    PSIStockVolumeType	VolumeCanceled;

    ///交易日
    PSIStockDateType	TradingDay;

    ///申报用户
    PSIStockUserIDType	InsertUser;

    ///申报日期
    PSIStockDateType	InsertDate;

    ///申报时间
    PSIStockTimeType	InsertTime;

    ///交易所接收时间
    PSIStockTimeType	AcceptTime;

    ///撤销用户
    PSIStockUserIDType	CancelUser;

    ///撤销时间
    PSIStockTimeType	CancelTime;

    ///经纪公司部门代码
    PSIStockDepartmentIDType	DepartmentID;

    ///资金账户代码
    PSIStockAccountIDType	AccountID;

    ///币种
    PSIStockCurrencyIDType	CurrencyID;

    ///交易单元代码
    PSIStockPbuIDType	PbuID;

    ///成交金额
    PSIStockMoneyType	Turnover;

    ///报单类型
    PSIStockOrderTypeType	OrderType;

    ///用户端产品信息
    PSIStockUserProductInfoType	UserProductInfo;

    ///强平原因(两融专用)
    PSIStockForceCloseReasonType	ForceCloseReason;

    ///信用头寸编号(两融专用)
    PSIStockQuotaIDType	CreditQuotaID;

    ///头寸类型(两融专用)
    PSIStockCreditQuotaTypeType	CreditQuotaType;

    ///信用负债编号(两融专用)
    PSIStockCreditDebtIDType	CreditDebtID;

    ///IP地址
    PSIStockIPAddressType	IPAddress;

    ///Mac地址
    PSIStockMacAddressType	MacAddress;

    ///回报附加浮点型数据信息
    PSIStockFloatInfoType	RtnFloatInfo;

    ///回报附加整型数据
    PSIStockIntInfoType	RtnIntInfo;

    ///回报附加浮点型数据1
    PSIStockFloatInfoType	RtnFloatInfo1;

    ///回报附加浮点型数据2
    PSIStockFloatInfoType	RtnFloatInfo2;

    ///回报附加浮点型数据3
    PSIStockFloatInfoType	RtnFloatInfo3;
};

/// 输入报单
struct PSIStockInputOrderField
{
    ///用户请求编号
    PSIStockRequestIDType	UserRequestID;

    ///交易所代码
    PSIStockExchangeIDType	ExchangeID;

    ///投资者代码
    PSIStockInvestorIDType	InvestorID;

    ///投资单元代码
    PSIStockBusinessUnitIDType	BusinessUnitID;

    ///股东账户代码
    PSIStockShareholderIDType	ShareholderID;

    ///证券代码
    PSIStockSecurityIDType	SecurityID;

    ///买卖方向
    PSIStockDirectionType	Direction;

    ///价格
    PSIStockPriceType	LimitPrice;

    ///数量
    PSIStockVolumeType	VolumeTotalOriginal;

    ///报单价格条件
    PSIStockOrderPriceTypeType	OrderPriceType;

    ///有效期类型
    PSIStockTimeConditionType	TimeCondition;

    ///成交量类型
    PSIStockVolumeConditionType	VolumeCondition;

    ///委托方式
    PSIStockOperwayType	Operway;

    ///报单引用
    PSIStockOrderRefType	OrderRef;

    ///港股通订单数量类型
    PSIStockLotTypeType	LotType;

    ///系统报单编号
    PSIStockOrderSysIDType	OrderSysID;

    ///条件检查
    PSIStockCondCheckType	CondCheck;

    ///有效日期
    PSIStockDateType	GTDate;

    ///强平原因(两融专用)
    PSIStockForceCloseReasonType	ForceCloseReason;

    ///指定偿还的信用负债编号（该字段置空表示不指定偿还）(两融专用)
    PSIStockCreditDebtIDType	CreditDebtID;

    ///头寸类型(两融专用)
    PSIStockCreditQuotaTypeType	CreditQuotaType;

    ///费息折扣券编号（0表示不使用折扣券）(两融专用)
    PSIStockIntSerialType	DiscountCouponID;

    ///字符串附加信息
    PSIStockStrInfoType	SInfo;

    ///整形附加信息
    PSIStockIntInfoType	IInfo;

    char	    exch[11];			// 交易所信息
    char        stra_uuid[64];       // 策略UUID
    char        sys_id[64];         // 交易订单号
    char        channel[64];         // 通道
    char        account_id[64];      // 账户ID
    char        order_type; // 订单类型
    char        msg[256]; // 交易消息
    int64_t     time; // 下单时间纳秒时间
};

/// 输入撤单操作
struct PSIStockInputOrderActionField
{
    ///用户请求编号
    PSIStockRequestIDType	UserRequestID;

    ///交易所代码
    PSIStockExchangeIDType	ExchangeID;

    ///被撤报单前置编号
    PSIStockFrontIDType	FrontID;

    ///被撤报单会话编号
    PSIStockSessionIDType	SessionID;

    ///被撤报单引用
    PSIStockOrderRefType	OrderRef;

    ///被撤报单系统编号
    PSIStockOrderSysIDType	OrderSysID;

    ///操作标志
    PSIStockActionFlagType	ActionFlag;

    ///报单操作引用
    PSIStockOrderRefType	OrderActionRef;

    ///系统撤单编号
    PSIStockOrderSysIDType	CancelOrderSysID;

    ///委托方式
    PSIStockOperwayType	Operway;

    ///字符串附加信息
    PSIStockStrInfoType	SInfo;

    ///整形附加信息
    PSIStockIntInfoType	IInfo;
};

/// 成交
struct PSIStockTradeField
{
    ///交易所代码
    PSIStockExchangeIDType	ExchangeID;

    ///经纪公司部门代码
    PSIStockDepartmentIDType	DepartmentID;

    ///投资者代码
    PSIStockInvestorIDType	InvestorID;

    ///投资单元代码
    PSIStockBusinessUnitIDType	BusinessUnitID;

    ///股东账户代码
    PSIStockShareholderIDType	ShareholderID;

    ///证券代码
    PSIStockSecurityIDType	SecurityID;

    ///成交编号
    PSIStockTradeIDType	TradeID;

    ///买卖方向
    PSIStockDirectionType	Direction;

    ///系统报单编号
    PSIStockOrderSysIDType	OrderSysID;

    ///本地报单编号
    PSIStockOrderLocalIDType	OrderLocalID;

    ///成交价格
    PSIStockPriceType	Price;

    ///成交数量
    PSIStockVolumeType	Volume;

    ///成交日期
    PSIStockDateType	TradeDate;

    ///成交时间
    PSIStockTimeType	TradeTime;

    ///交易日
    PSIStockDateType	TradingDay;

    ///交易单元代码
    PSIStockPbuIDType	PbuID;

    ///报单引用
    PSIStockOrderRefType	OrderRef;

    ///资金账户代码
    PSIStockAccountIDType	AccountID;

    ///币种
    PSIStockCurrencyIDType	CurrencyID;
};

/// 投资者持仓
struct PSIStockPositionField
{
    ///交易所代码
    PSIStockExchangeIDType	ExchangeID;

    ///投资者代码
    PSIStockInvestorIDType	InvestorID;

    ///投资单元代码
    PSIStockBusinessUnitIDType	BusinessUnitID;

    ///市场代码
    PSIStockMarketIDType	MarketID;

    ///股东账户代码
    PSIStockShareholderIDType	ShareholderID;

    ///交易日
    PSIStockDateType	TradingDay;

    ///证券代码
    PSIStockSecurityIDType	SecurityID;

    ///证券名称
    PSIStockSecurityNameType	SecurityName;

    ///昨仓
    PSIStockVolumeType	HistoryPos;

    ///昨仓冻结
    PSIStockVolumeType	HistoryPosFrozen;

    ///今买卖仓
    PSIStockVolumeType	TodayBSPos;

    ///今买卖仓冻结
    PSIStockVolumeType	TodayBSPosFrozen;

    ///今日申赎持仓
    PSIStockVolumeType	TodayPRPos;

    ///今日申赎持仓冻结
    PSIStockVolumeType	TodayPRPosFrozen;

    ///今拆分合并持仓
    PSIStockVolumeType	TodaySMPos;

    ///今拆分合并持仓冻结
    PSIStockVolumeType	TodaySMPosFrozen;

    ///昨仓成本价
    PSIStockPriceType	HistoryPosPrice;

    ///持仓成本
    PSIStockMoneyType	TotalPosCost;

    ///上次余额(盘中不变)
    PSIStockVolumeType	PrePosition;

    ///股份可用
    PSIStockVolumeType	AvailablePosition;

    ///股份余额
    PSIStockVolumeType	CurrentPosition;

    ///开仓成本
    PSIStockMoneyType	OpenPosCost;

    ///融资仓位(两融专用)
    PSIStockVolumeType	CreditBuyPos;

    ///融券仓位(两融专用)
    PSIStockVolumeType	CreditSellPos;

    ///今日融券仓位(两融专用)
    PSIStockVolumeType	TodayCreditSellPos;

    ///划出仓位(两融专用)
    PSIStockVolumeType	CollateralOutPos;

    ///还券未成交数量(两融专用)
    PSIStockVolumeType	RepayUntradeVolume;

    ///直接还券未成交数量(两融专用)
    PSIStockVolumeType	RepayTransferUntradeVolume;

    ///担保品买入未成交金额(两融专用)
    PSIStockMoneyType	CollateralBuyUntradeAmount;

    ///担保品买入未成交数量(两融专用)
    PSIStockVolumeType	CollateralBuyUntradeVolume;

    ///融资买入金额(包含交易费用)(两融专用)
    PSIStockMoneyType	CreditBuyAmount;

    ///融资买入未成交金额(包含交易费用)(两融专用)
    PSIStockMoneyType	CreditBuyUntradeAmount;

    ///融资冻结保证金(两融专用)
    PSIStockMoneyType	CreditBuyFrozenMargin;

    ///融资买入利息(两融专用)
    PSIStockMoneyType	CreditBuyInterestFee;

    ///融资买入未成交数量(两融专用)
    PSIStockVolumeType	CreditBuyUntradeVolume;

    ///融券卖出金额(以成交价计算)(两融专用)
    PSIStockMoneyType	CreditSellAmount;

    ///融券卖出未成交金额(两融专用)
    PSIStockMoneyType	CreditSellUntradeAmount;

    ///融券冻结保证金(两融专用)
    PSIStockMoneyType	CreditSellFrozenMargin;

    ///融券卖出息费(两融专用)
    PSIStockMoneyType	CreditSellInterestFee;

    ///融券卖出未成交数量(两融专用)
    PSIStockVolumeType	CreditSellUntradeVolume;

    ///划入待收仓(两融专用)
    PSIStockVolumeType	CollateralInPos;

    ///融资流动冻结保证金(两融专用)
    PSIStockMoneyType	CreditBuyFrozenCirculateMargin;

    ///融券流动冻结保证金(两融专用)
    PSIStockMoneyType	CreditSellFrozenCirculateMargin;

    ///累计平仓盈亏(两融专用)
    PSIStockMoneyType	CloseProfit;

    ///当日累计开仓数量
    PSIStockVolumeType	TodayTotalOpenVolume;

    ///今手续费
    PSIStockMoneyType	TodayCommission;

    ///当日累计买入金额
    PSIStockMoneyType	TodayTotalBuyAmount;

    ///当日累计卖出金额
    PSIStockMoneyType	TodayTotalSellAmount;

    ///上日冻结(盘中不变)
    PSIStockVolumeType	PreFrozen;
};

/// 资金账户
struct PSIStockTradingAccountField
{
    ///经纪公司部门代码
    PSIStockDepartmentIDType	DepartmentID;

    ///资金账户代码
    PSIStockAccountIDType	AccountID;

    ///币种代码
    PSIStockCurrencyIDType	CurrencyID;

    ///上日结存
    PSIStockMoneyType	PreDeposit;

    ///可用资金
    PSIStockMoneyType	UsefulMoney;

    ///可取资金
    PSIStockMoneyType	FetchLimit;

    ///上日未交收金额(港股通专用字段)
    PSIStockMoneyType	PreUnDeliveredMoney;

    ///可用未交收金额(港股通专用字段)
    PSIStockMoneyType	UnDeliveredMoney;

    ///当日入金金额
    PSIStockMoneyType	Deposit;

    ///当日出金金额
    PSIStockMoneyType	Withdraw;

    ///冻结的资金(港股通该字段不包括未交收部分冻结资金)
    PSIStockMoneyType	FrozenCash;

    ///冻结未交收金额(港股通专用)
    PSIStockMoneyType	UnDeliveredFrozenCash;

    ///冻结的手续费(港股通该字段不包括未交收部分冻结手续费)
    PSIStockMoneyType	FrozenCommission;

    ///冻结未交收手续费(港股通专用)
    PSIStockMoneyType	UnDeliveredFrozenCommission;

    ///手续费(港股通该字段不包括未交收部分手续费)
    PSIStockMoneyType	Commission;

    ///占用未交收手续费(港股通专用)
    PSIStockMoneyType	UnDeliveredCommission;

    ///资金账户类型
    PSIStockAccountTypeType	AccountType;

    ///资金账户所属投资者代码
    PSIStockInvestorIDType	InvestorID;

    ///银行代码
    PSIStockBankIDType	BankID;

    ///银行账户
    PSIStockBankAccountIDType	BankAccountID;

    ///权利金收入(两融专用)
    PSIStockMoneyType	RoyaltyIn;

    ///权利金支出(两融专用)
    PSIStockMoneyType	RoyaltyOut;

    ///融券卖出金额(两融专用)
    PSIStockMoneyType	CreditSellAmount;

    ///融券卖出使用金额(用于偿还融资负债或买特殊品种的金额)(两融专用)
    PSIStockMoneyType	CreditSellUseAmount;

    ///虚拟资产(两融专用)
    PSIStockMoneyType	VirtualAssets;

    ///融券卖出金额冻结(用于偿还融资负债或买特殊品种的未成交冻结金额)(两融专用)
    PSIStockMoneyType	CreditSellFrozenAmount;

    ///属主单元
    PSIStockBusinessUnitIDType	OwnerUnit;
};
#pragma pack(pop)

