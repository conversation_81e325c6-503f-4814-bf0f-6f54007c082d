#ifndef TCPSHMCLIENTWRAPPER_H
#define TCPSHMCLIENTWRAPPER_H

#include <QWidget>
#include <QThread>
#include <QDebug>
#include <QCoreApplication>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "../network/PsiTcpShmClient.h"
#include <QScrollBar>
#include <QTextEdit>
#include <QLabel>
#include <QTextFrame>
#include <QTextCharFormat>

struct TcpShmConfig {
    int choice;
    QString clientName;
    QString serverAddr;
    int serverPort;
    bool useShm;
    int tcpRecvBufInitSize;
    int tcpRecvBufMaxSize;
    bool tcpNoDelay;
    qint64 connectionTimeout;
    qint64 heartBeatInterval;
};

class TcpShmClientWrapper : public QWidget
{
    Q_OBJECT

public:
    explicit TcpShmClientWrapper(QWidget *parent = nullptr);
    ~TcpShmClientWrapper();
    void resizeEvent(QResizeEvent *event) override;  // 重写 resizeEvent

    // 回调函数类型
    using MessageCallback = std::function<void(const QString&,const QString&)>;

    // 设置回调函数,Client调用它来传递消息
    void setMessageCallback(MessageCallback callback);

    // 添加消息到显示区域
    void appendMessage(const QString &flag,const QString &message);

    // 更新连接状态
    void updateConnectStatus(bool connected,QString text);


private:
    Client *m_client = nullptr;
    ClientConf m_clientConf;
    TcpShmConfig parseConfig(const QString& filePath);
    MessageCallback m_callback;
    QTextEdit *textEdit;
    QLabel * statusIndicator;
    QLabel * statusLabel;
};

#endif // TCPSHMCLIENTWRAPPER_H
