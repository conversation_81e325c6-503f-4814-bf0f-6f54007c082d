#include "tcpshmclientwrapper.h"
#include <QHBoxLayout>
#include <QStringDecoder>

TcpShmClientWrapper::TcpShmClientWrapper(QWidget *parent)
    : QWidget{parent}
{
    QString configPath = QCoreApplication::applicationDirPath() + "/config.yaml";

    TcpShmConfig config = parseConfig(configPath);

    // 初始化配置
    m_clientConf.Index = 1;
    m_clientConf.CpuCore = 0; // 不绑定核心
    m_clientConf.AccountId = config.clientName.toStdString();
    m_clientConf.ServerAddr = config.serverAddr.toStdString();
    m_clientConf.ServerPort = config.serverPort;
    m_clientConf.ConnectionTimeout = config.connectionTimeout;
    m_clientConf.HeartBeatInverval = config.heartBeatInterval;
    QString qtPath = QString::fromUtf8("D:/tmp/tcp");
    // 转换为 UTF-8 编码
    QByteArray utf8Data = qtPath.toUtf8();
    const std::string stdPath(utf8Data.constData(), utf8Data.length());
    // 创建客户端实例
    m_client = new Client(stdPath, config.clientName.toStdString(), m_clientConf);
    m_client->m_useShm = false; // 不使用共享内存

    // 初始化文本显示区域
    textEdit = new QTextEdit(this);
    textEdit->setReadOnly(true); // 设置为只读
    textEdit->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOn); // 始终显示垂直滚动条

    QWidget * statusContainer = new QWidget(this);
    QHBoxLayout * statusLayout = new QHBoxLayout(statusContainer);
    statusLayout->setContentsMargins(0,0,0,0);
    statusLayout->setSpacing(5);

    statusIndicator = new QLabel();
    statusIndicator->setFixedSize(12,12);
    statusIndicator->setStyleSheet("border-radius:6px;background:red;");

    statusLabel = new QLabel("连接中...");
    statusLayout->addWidget(statusIndicator);
    statusLayout->addWidget(statusLabel);

    QVBoxLayout * mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(10,10,10,10);
    mainLayout->addWidget(textEdit);
    mainLayout->addWidget(statusContainer);

    updateConnectStatus(false,"连接中...");

    // 设置状态回调
    m_client->setStatusHandler([this](bool connected, const std::string &text) {
        updateConnectStatus(connected, QString::fromStdString(text));
    });

    m_client->Run();


    // 设置回调函数，Client 调用它来传递消息
    setMessageCallback([this](const QString &flag,const QString &msg) {
        appendMessage(flag,msg);
    });
}

void TcpShmClientWrapper::updateConnectStatus(bool connected, QString text)
{
    // 使用 QMetaObject::invokeMethod 确保在 UI 线程中执行
    QMetaObject::invokeMethod(this, [=]() {
        if (connected)
        {
            statusIndicator->setStyleSheet("border-radius:6px;background:green;");
        }
        else
        {
            statusIndicator->setStyleSheet("border-radius:6px;background:red;");
        }
        statusLabel->setText(text);
    });
}

TcpShmConfig TcpShmClientWrapper::parseConfig(const QString& filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "Cannot open config file";
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qDebug() << error.errorString().toStdString();
    }

    TcpShmConfig config;
    QJsonObject root = doc.object();
    QJsonObject traderClient = root["tcpshm"].toObject()
                                   ["client"].toObject()
                                           ["trader_client"].toObject();

    config.choice = traderClient["choice"].toInt();

    QJsonArray options = traderClient["options"].toArray();
    if (!options.isEmpty()) {
        QJsonObject option = options[0].toObject();
        config.clientName = option["client_name"].toString();
        config.serverAddr = option["server_addr"].toString();
        config.serverPort = option["server_port"].toInt();
        config.useShm = option["use_shm"].toBool();
        config.tcpRecvBufInitSize = option["tcp_recv_buf_init_size"].toInt();
        config.tcpRecvBufMaxSize = option["tcp_recv_buf_max_size"].toInt();
        config.tcpNoDelay = option["tcp_no_delay"].toBool();
        config.connectionTimeout = option["connection_timeout"].toVariant().toLongLong();
        config.heartBeatInterval = option["heart_beat_interval"].toVariant().toLongLong();
    }

    return config;
}

void TcpShmClientWrapper::setMessageCallback(MessageCallback callback) {
    m_callback = callback;
    if (m_client) {
        m_client->setMessageHandler([callback](const std::string &flag,const std::string &msg) {
            callback(QString::fromStdString(flag),QString::fromStdString(msg));
        });
    }
}

// GBK 转 UTF-8
QString gbkToUtf8(const QByteArray &gbkData)
{
    QStringDecoder decoder(QStringDecoder::System); // 或明确指定 "GBK"
    return decoder.decode(gbkData);
}

void TcpShmClientWrapper::appendMessage(const QString &flag,const QString &message)
{
    // 将可能GBK编码的消息转换为UTF-8
    QString utf8Message = QString::fromLocal8Bit(message.toLocal8Bit());

    // Qt事件系统自动将操作派发到UI线程执行
    QMetaObject::invokeMethod(textEdit, [=]() {
        QTextCursor cursor = textEdit->textCursor();
        cursor.movePosition(QTextCursor::End);

        // 设置文本块格式
        QTextBlockFormat blockFormat;
        blockFormat.setTopMargin(5);
        blockFormat.setBottomMargin(5);
        cursor.setBlockFormat(blockFormat);

        // 设置字符格式
        QTextCharFormat charFormat;
        if (flag == "sent") {
            charFormat.setBackground(QColor(232, 248, 255));
        } else {
            charFormat.setBackground(QColor(220, 220, 220));
        }
        cursor.setCharFormat(charFormat);

        // 插入文本
        cursor.insertText(" " + utf8Message + " ");
        cursor.insertText("\n");


        // 自动滚动到底部
        QScrollBar *scrollBar = textEdit->verticalScrollBar();
        scrollBar->setValue(scrollBar->maximum());
    });
}

void TcpShmClientWrapper::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);  // 调用父类的 resizeEvent
}

TcpShmClientWrapper::~TcpShmClientWrapper()
{
    delete m_client;
}
