#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include "globals.h"
#include "tcpshmclientwrapper.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    void resizeEvent(QResizeEvent *event) override;  // 重写 resizeEvent
private:
    // Ui::MainWindow *ui;
    TcpShmClientWrapper * client = nullptr;
};

#endif // MAINWINDOW_H
