cmake_minimum_required(VERSION 3.16)
project(MyNetworkApp)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 自动处理 Qt 的 moc
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 查找 Qt
find_package(Qt6 REQUIRED COMPONENTS Core Gui Widgets Network)

# 查找 Boost
find_package(Boost REQUIRED COMPONENTS filesystem system)

# 添加网络框架源文件
add_subdirectory(network)

# 给 PsiNetwork 添加 atomic_queue 头文件路径
target_include_directories(PsiNetwork PRIVATE
    "D:/atomic_queue/include"
)

# 主程序可执行文件
add_executable(${PROJECT_NAME}
    src/main.cpp src/mainwindow.h src/mainwindow.cpp src/mainwindow.ui
    src/tcpshmclientwrapper.h src/tcpshmclientwrapper.cpp
    src/globals.h
    src/res.qrc


)


target_link_libraries(MyNetworkApp PRIVATE Qt6::Widgets)
set(BOOST_ROOT "D:/Boost")
find_package(Boost 1.88 REQUIRED COMPONENTS filesystem)

# 添加网络框架头文件路径
target_include_directories(
    ${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/network
    ${Boost_INCLUDE_DIRS}
        "D:/plugin/Rapidjson/rapidjson/include"
        "D:/atomic_queue/include"
        "D:/plugin/Hiredis/hiredis"
)

#include_directories(${Boost_INCLUDE_DIRS})
#include_directories("D:/plugin/Rapidjson/rapidjson/include")  # 直接指定绝对路径
#set(ATOMIC_QUEUE_INCLUDE_DIR "D:/atomic_queue/include")
#include_directories(${ATOMIC_QUEUE_INCLUDE_DIR})

#include_directories("D:/atomic_queue/include")  # 直接指定绝对路径
include_directories("D:/plugin/Hiredis/hiredis")  # 直接指定绝对路径
# 链接库
target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt6::Core
    Qt6::Gui
    Qt6::Widgets
    Qt6::Network
    PsiNetwork
    ${Boost_LIBRARIES}
    ${CMAKE_THREAD_LIBS_INIT}
)

# Windows 特定设置
if(WIN32)
    target_link_libraries(${PROJECT_NAME} PRIVATE
        ws2_32
        advapi32
    )
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
endif()
